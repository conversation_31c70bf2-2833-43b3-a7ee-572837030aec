%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Roman
%%Title: Mysterious gangster mafia character - up-01.eps
%%Creator: Adobe Illustrator(R) 24.0
%%For: Ayib <PERSON>k<PERSON>n B9
%%CreationDate: 2/27/2020
%%BoundingBox: 0 0 500 500
%%HiResBoundingBox: 0 0 500 500
%%CropBox: 0 0 500 500
%%LanguageLevel: 2
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 24.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 24.0.1 x341 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
2 2010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 1 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c148 79.164050, 2019/10/01-18:03:16        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Mysterious gangster mafia character - up-01</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-02-27T19:52:26+07:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-02-27T19:52:26+07:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-02-27T19:52:26+07:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator 24.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>256</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+ICQElDQ19QUk9GSUxFAAEBAAACMEFEQkUCEAAAbW50clJHQiBYWVogB88ABgAD&#xA;AAAAAAAAYWNzcEFQUEwAAAAAbm9uZQAAAAAAAAAAAAAAAAAAAAAAAPbWAAEAAAAA0y1BREJFAAAA&#xA;AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKY3BydAAAAPwAAAAy&#xA;ZGVzYwAAATAAAABrd3RwdAAAAZwAAAAUYmtwdAAAAbAAAAAUclRSQwAAAcQAAAAOZ1RSQwAAAdQA&#xA;AAAOYlRSQwAAAeQAAAAOclhZWgAAAfQAAAAUZ1hZWgAAAggAAAAUYlhZWgAAAhwAAAAUdGV4dAAA&#xA;AABDb3B5cmlnaHQgMTk5OSBBZG9iZSBTeXN0ZW1zIEluY29ycG9yYXRlZAAAAGRlc2MAAAAAAAAA&#xA;EUFkb2JlIFJHQiAoMTk5OCkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&#xA;AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAADzUQABAAAA&#xA;ARbMWFlaIAAAAAAAAAAAAAAAAAAAAABjdXJ2AAAAAAAAAAECMwAAY3VydgAAAAAAAAABAjMAAGN1&#xA;cnYAAAAAAAAAAQIzAABYWVogAAAAAAAAnBgAAE+lAAAE/FhZWiAAAAAAAAA0jQAAoCwAAA+VWFla&#xA;IAAAAAAAACYxAAAQLwAAvpz/7gAOQWRvYmUAZMAAAAAB/9sAhAAGBAQEBQQGBQUGCQYFBgkLCAYG&#xA;CAsMCgoLCgoMEAwMDAwMDBAMDg8QDw4MExMUFBMTHBsbGxwfHx8fHx8fHx8fAQcHBw0MDRgQEBga&#xA;FREVGh8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx//wAAR&#xA;CAEAAQADAREAAhEBAxEB/8QBogAAAAcBAQEBAQAAAAAAAAAABAUDAgYBAAcICQoLAQACAgMBAQEB&#xA;AQAAAAAAAAABAAIDBAUGBwgJCgsQAAIBAwMCBAIGBwMEAgYCcwECAxEEAAUhEjFBUQYTYSJxgRQy&#xA;kaEHFbFCI8FS0eEzFmLwJHKC8SVDNFOSorJjc8I1RCeTo7M2F1RkdMPS4ggmgwkKGBmElEVGpLRW&#xA;01UoGvLj88TU5PRldYWVpbXF1eX1ZnaGlqa2xtbm9jdHV2d3h5ent8fX5/c4SFhoeIiYqLjI2Oj4&#xA;KTlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+hEAAgIBAgMFBQQFBgQIAwNtAQACEQMEIRIxQQVR&#xA;E2EiBnGBkTKhsfAUwdHhI0IVUmJy8TMkNEOCFpJTJaJjssIHc9I14kSDF1STCAkKGBkmNkUaJ2R0&#xA;VTfyo7PDKCnT4/OElKS0xNTk9GV1hZWltcXV5fVGVmZ2hpamtsbW5vZHV2d3h5ent8fX5/c4SFho&#xA;eIiYqLjI2Oj4OUlZaXmJmam5ydnp+So6SlpqeoqaqrrK2ur6/9oADAMBAAIRAxEAPwD1HhYuxV2K&#xA;uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Ku&#xA;xV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV5F+Zn/ORHl7yvNNpeixrrOtREpMQ1LWBx2kcbuwPV&#xA;U+lgcthiJ5uJm1YjsNy8J1T8/fzV1C4Mv6aa0SvJILWKKNF9vslyP9Zjl4xRcGWqyHq9Q8if85U6&#xA;fJHHY+crVoblaJ+k7NeSOf5pIPtL0rVOX+qMpli7nMx6v+c9V0/83Pyyv4+cPmWwQeFxMtswp4rP&#xA;6bDr4ZDgPc5AzwPUKl7+an5bWSF5vM2mkCtVhuY5m2/yYi7fhg4D3Kc0B1DzTzv/AM5T+XrCOS18&#xA;q2r6le7qt3cq0NuppsRGaSv8iF+eWDF3tE9WP4XkFv8An3+a8F890uuvJ6jl2gkiheLc14hCnwjf&#xA;9mmXeFFwvzWS+b2X8t/+cl9I1mWPTvNkUek30hCxX8dfqbk9A/Is0J+ZK+4ymeGuTmYdYDtLZ7cr&#xA;KyhlIKkVBG4IOUua3irsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVePf8A&#xA;OQXnzVtItLPy3pEjW1xq8ckl5eoaSJbIQhSLwaQtTl2Hzy7FG93E1WUj0jq+U724jkk4QKEt49o1&#xA;Hf8AyjXucyXVEofCha8aPTkK07jr1yJDOMqUjajiKPVu4IoPvrg4WfiBUntqzzF2oebUAFe5+WPC&#xA;pyByRIn2Rv4nrhAYSna/JMFa1uPRc1USRMOMkZ6MP6jscCQX0p/zjt5+1SS8Pk6/lN1Yi2a50a5d&#xA;v3kaRleUBr8TJxcMngNunTHyx6uy0mU3wl73lDnOxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV8uf85Iaksvn24UE/7jdMgt6LTaSd5ZK+P7SZk4Rs6zWH1+4PDcvcF2KuxV2K&#xA;qlz/AL0S/wCu368CSp4UOxV2KvVPyJ1MWv5g+W5mf4ZJJ7GWpPWaGT0x/wAEqZVkGxcrTGph9g5i&#xA;O3dirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir4m/N/WG1Hz/5puQ9Ve/Fo&#xA;AOjLZL6O3/ItczMY2Dpc8rmfewPLGh2KuxV2Kqlz/vRL/rt+vAkqeFDsVdiqf+UtbfSdU02+58U0&#xA;7UrW8I7kI1W+ii0ORkLbMcqIPcX3tmC712KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Ku&#xA;xV2KuxVQv7yGxsbm9nNIbWJ5pT4LGpZvwGEIJoW/Pi8vJ7y5muZzylnkeaRvF5DyY/fmc6Am1DCh&#xA;2KuxV2Kqlz/vRL/rt+vAkqeFDsVdirYYgMB0YUP31/hgS+9fy/1b9L+R9B1Itye5sbdpT/xYIwJP&#xA;+HBzCkKLvMUriD5J/kWx2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KqN3eWlnA093PHb&#xA;wJ9qWVlRB82YgYqTTE9T/OP8r9NJFz5ks2K9RbubntX/AHQJcmMcu5pOogOrBPzB/Pz8vtT8o6xp&#xA;Gi3091fahbS2sRjt5kFJRwdqyKlPgYkbZOOI205dVAxID5i/R0zgtA63CileFFcVr1Q0H45kWXXc&#xA;IPJDyQyx7OpQ1AowK7nsK0r9GG2JiVhBBoRQjqDih2FDsVVLn/eiX/Xb9eBJU8KHAEmg64FXLFIz&#xA;FVUkg0YAEkfMCpxtkIlEjTbgLymK26d2lIruD0UV/Xgtlwgc30T+UH52eR/LXkiw0DXbyeC5s2l9&#xA;OQ28jq0csrSAj0w1N3IpToMonjJNhz8OphGNF6Xp351/lXqBAg8x2qFun1jnbD6TOsdMrOOXc5A1&#xA;GM9WW2Gp6bqEPr2F3DeQ/wC/YJFlX/gkJGRIbQQeSJwJdirsVdirsVdirsVdirsVdirsVdirsVdi&#xA;qWeY/MmieW9Jm1bWbpLSxh+1I25Zj0RFHxMx7AYQCeTGcxEWXzX55/5ye8zalLLbeV4xo1gKhbmR&#xA;Vlu3378uUcdR2AYj+bMiOIdXXZdZI8tnj2q63rGr3BudVvri/nP+7bmV5W393Jy4CnDlInmgsLFM&#xA;/L4Q3khNCVjIG4qDyXt16ZG2cQnUlpbSnk8alv5qUb7xviyp0duiCgZiK1ozFt/priq1rGzIYGFQ&#xA;H+1QUr37YpUW0ewYAenQDpQn9fU/Tiig0NF08fsE/wCyONo4QvuNFsPWk+A15HfkfHG0mIWpo9gv&#xA;+66+NTXFaCqmn2SqEEKlVNQGHLelK71xSqPAjKVqVU9Qp49Pliq2Ozto25LGC/8AO3xNv/lGpxRS&#xA;V+Y1jHoPsHPMMSabDjTr8zjaJC0n6bHCwRFhqWo6dcLc6fdTWdyv2Z7eRonHyZCDiQoJHJ6x5K/5&#xA;yV86aK8cOuFdd04EB/WpHdIu26zKKN3+2DXxGVSxAuZi1chz3fTHk3zr5e84aOuqaJcerDXhNEw4&#xA;ywyUqUkTsfwPauY8okc3Y48gmLCe5Fm7FXYq7FXYq7FXYq7FXYq7FXYq7FXyf+duu3/nT8zpPLMV&#xA;2trZ6YzWWmpIeMcl5xBcMxNEeST90rHYUHTc5kwFRt1meXHkru5e95Pq+i6to149lqllNY3SEgwT&#xA;o0bbbVHIbjwI2y0EHk4k4kHdBZJg75YEq1hdy2MzSQgMj0EkR2rTwPY5GmwTvmyC11ayuKKH9OQ/&#xA;7rk+Enem3Y/QcbTSMwq7FXYq7FVSf+/k/wBY/rxUqeKuxV2KoW61KztqiSQGQV/dJ8T7CvQdPpwW&#xA;tMf1K9kv5VLoI4oyeCA1Jr15N9HbGrQZAIfJNZLsKHYEs4/Jzzpe+VPPGn3EUjDTryWO01OHcoYJ&#xA;WCcm94y3JO/bp1hONhyMGXhkD8327mG7h2KuxV2KuxV2KuxV2KuxV2KuxV2Kvjn89vIfmHSPPWra&#xA;s1nNLo+oztdwX6IWiBlHN0dlqEKtyFG7CuZeKYIdTqsMhInoU38k/wDOQfo6dHofnzTE8xaUnwpd&#xA;SIk06r4SJN8EtPEkHxJyMsPUNmLWbVMWGZL+W/5GfmTbSSeUbsaVqwXm0MHJGT/jJaSkfCPGOg98&#xA;hxzjzbvBxZR6dj+Ojw7z/wDl55h8kax+j9WjDRyAtZ3sdTDOg7qSBRh+0p3HyoTfCYkHAzYZYzRY&#xA;xk2l3sdx1odxtgpINKsF1cQcPSldFQk8A1V3/wAluS/hgplxouLXL9VYOyPU1VmSp+Xwsg/DGikS&#xA;CvB5hkpSaBCf5g5UfdwfGikSC8+YDXaFKf8AGRv+qeDdNjvXz+YG9aSsKA8jX943j/xjOO6kjvU5&#xA;vMLBf3VuvI9CZCwB9xwTHdBkEPLrt+0XFDGjnqwQ1HyqxH4YaKOIIWe+u5y3qTOysvEpXiKe4QID&#xA;9IxpeNQ2AoAAPAbD8MNMCSXYUOxV2KuxVkHkDy/c+YfOej6TboWNxdR+qR+zCjc5XNP5Y1JyMjQb&#xA;MUOKQD71zBd67FXYq7FXYq7FXYq7FXYq7FXYq7FVp64CzDH9d/L/AMk68jDVtEs7pm6zGJUl+iVO&#xA;Mg+hsImRyYSxRlzDxjz1/wA4+Xfl1/8AFP5eXk8V3px+sjTmblKAm5NvJ1bbqj15CoqemXRzXtJw&#xA;smk4fVDoy782LS282fkaNbvIRHepY2urW5pvFK6I7qO9CrstMhjNTpuzgTxWe63yLma6V2KuxV2K&#xA;uxV2Kqlz/vRL/rt+vAkqeFDsVdirsVdirsVdgSjNH0fVdZ1GHTdKtZLy+nPGKCIcmJ/gB3J2HfEk&#xA;MhAk0Ob67/Jb8nbbyNp7X1+UuPMl6gW5lXdII619GI996c27n2GYmTJxO10+n4BZ5vTsrcl2KuxV&#xA;2KuxV2KuxV2KuxV2KuxS1XBa0474VayLJ2KvJ/8AnIvU7/T/AMuf0RpNlLINRkjt5Xhido4LWGjt&#xA;yZFYJy4qgB6itOmW4hZtxdXKoV3vkNiVDlkIANFAIJ+n7J/DMu3U8I73O6oQHJVj1Uggj51GPEjg&#xA;LmZF6su/gwP6jhtBiV5jkC8ipC+JG2NoorcVdhQqXP8AvRL/AK7frwJKnhQuVHYVVSw6VArgtNFY&#xA;CpbjyAI61IH68bTwlyujOUDVYdAATWnhQHBxBIgWgzFfhRiwNCpov3b1/DG08I72Q6B+X/nfzAwG&#xA;j6LdXUbEcZ0iYRU8DPJwiH05EyrmWccZPIEvWfKX/OKmvXTJN5o1GPT4CQzWlpSec+ILkCKM+68h&#xA;lZyjo5UNJI8zT3vyd+X/AJT8n2ZttCsEt2cAT3TfHPLT+eVviI78fsjsMplInm5mPFGA2ZFkWx2K&#xA;uxV2KuxV2KuxV2KuxV2KuxVpsBZBrAlsYQgt4WK1qCm/XYYCyDA/zF80a1ps8VhZH6vDPFzN0v2y&#xA;alSqt+zTbpvlGSZGwen7C7Nw5onJP1EHl0/b9z5Y/NbQb631h9Yj5vZXxDSuKkJMAFatOnLtmdgn&#xA;xRDyfbehOn1M4kemRMo91H9XJiOoJLbNaTI/KK4gjmjagG+6SD4QOkiMPll1uooISS5aRgxRajvu&#xA;a/OpONppf9aiKcXhBJ6leK/8a1wLSxpINjHGysO5ev8AAYVorvrkv87/APBtitHvXz3UonkAZ6Bm&#xA;GzsO+K0e9SEsRYtKjOT350P4g4rRXi5gVSEhFexbi361wLSxbkq/Pgle21KH24kYbWkXpqzX2oKr&#xA;Gkaq8s5G9IokMklOXLfippXvjaCAnv5d6Be6vr0VwwcafaSCa5kGykoeSoOxJPbwyE58IsuZotHL&#xA;UZo4o85H5DqfgH015c/MbVNNKQX1b2yG25/eoP8AJbv8j94zWQykc30rX9gYsovH6JfZ8v1PT9K1&#xA;jTtVtRc2MwljOzDoynwZTuDmTGQPJ4vU6XJglwzFFG4XHdirsVdirsVdirsVdirsVdirsVdirRGA&#xA;pDWBk7FWpHdY2KLzcA8UJoCewrvTDaxiCdzswSXS/wAwJtfg1aUQkW7ExWolpGqNsyjbqVP2soIm&#xA;Tb08NRoI4DiHF6uZre+/9jLr7S7DVrNYdStQ69fTc/ErdKqyHb6DlhAPN0WHUzwTvHL8e4sT1f8A&#xA;KXQby2ligkeMSqQYZuMsJHgVIDf8NgEDHeJpz8na/jx4NRCOSPyPwPT4PAfzJ/KLUrLy4j6Zalxo&#xA;95JbS2yvydEnBmqvKjMlacNydzmTgyGQo83Sds6DFglGWEnwpxvfvsvJ/wDDXmP/AKtV5/0jy/8A&#xA;NOX06Wwo3Oj6vaoXubG4gQdWkidB97AYpQeKp5o/kzzFqyLLa2pW3bpcSkRoR4iu7D/VBw0gyARG&#xA;u+RvMunNLcS2vqwAlmkgIk49SeSj4hTuaUxpeIFjeBKKtdL1O7FbW0muB4xRu/8AxEHFUR/hrzH/&#xA;ANWq8/6R5f8AmnFbei/lv+WmqS6PrGr6rC9nZsiWSqzBJXExrIqqQxGyitabVyrNk4I+btexezxq&#xA;9QIS+je658v10zyysrSxtUtLOFYLaMUSJBQfT3J9zmsnklLmX1DQ9m4NKKxRq+Z6n4/gK2Qc5HaR&#xA;rOoaReLdWUpjkH2lO6uv8rjuMlGRHJxtVpMeeHDMWPu9z2Lyv5qsdeteUf7q7jA+sWxO6/5S+K++&#xA;ZcJiT5/2j2bPSyo7xPI/jqneTda7FXYq7FXYq7FXYq7FXYq7FXYq7FWqDFNqN3OttbSzlHl9NS3p&#xA;xKXdqdlUbknIltxQM5COwvv2DD186+anYhfLNwB1HL1Bt8zGMp8SXc9AeyNKOeeP2frXnzR52b4k&#xA;8ukKegZjX+GHjl3Mf5O0Q55mrX8xPRvUs9c06TTZHoBKxJXfapDBTx9xXEZe8Lk7C4oceCYyD8fj&#xA;ozLrlrzzBNfCXuqeZtJQcpJLCK5AHX1bajgD3IcYMUqyOx1+mMuzoS7pS+39sXlObJ4p2KpVdReX&#xA;p75hLaQ3d9EObkQrLIgG45NQ8TtsK1PbFNlF2Opaffoz2dxHOENH9NgSp8GHVT88VIpM9Uk0Kx0N&#xA;dRutQjgumunhkglZUAj4hlYVpuTXvkb3ZmI4b62x6P8Aw4buK5a1hiuro1t7mSFUaQnpxkIrU9gT&#xA;XCx3TjCxdirLNQX6l5H0i0pR7+ea9k8fh/dp96nNbrJb0+g+xmnoSn5V8/7AxzMJ7t2KuxVF6Xql&#xA;5pd9He2b8Joz9DDurDuDhjIg20anTQzQMJiwXt3l/XbXWtMjvbfYn4Zou6SDqp/h7ZmxlYt8312j&#xA;lp8hhL4HvCZZJw3Yq7FXYq7FXYq7FXYq7FXYq7FXYqwjXvzLt9N1Kext7P60YDweYy8F5jqAOLVo&#xA;duuUSy0ael0Xs/LNjE5S4eLpV7fNjt9+aOvzAraxw2inowUyOPpb4f8AhcrOUu2w+zmCP1GUvs+7&#xA;9aRS+avMssnqNqdyG8EkZF/4FSF/DIcZ73Zx7N04FDHH5Wm+qa3Lrnk9ZLwiTUNOukRpaAM0UqNQ&#xA;mlO60PyyRlcXA02kGm1dQ2x5IHbzBD1HRyx0myL15G3i5V614CuZMeTxeq/vZ1/OP3vAtb/Nyw8u&#xA;/mVFcSj1o5btl1GlT6VpJWMHb7TKtGp7e4xwYTImTte2u0sen08NKN5VHi/o9fmfu94R3mnSF0vV&#xA;5I4SHsZwLiwmU8leCT4kKsOtOmbCErDwmfHwSrok0jhI2c7hQTT5ZJpSe2l0vy9pgfULiOCWYma5&#xA;kY/FJM/xOQN2bwFO2Katj8GteXvMWurBpYvLO+AdjqFsEjVlA39UEnkvSnJeuR5s6ICrrWoaN5d1&#xA;yNtZN7qVxKrNDcSiN4o1LUIRAUXltuaVxXc8k8S+0DzNp0tvb3KTpIu6g0kjPZuJoylT0OSYUQmV&#xA;i0xs4fX/AL8Iol/1wKN198UFNdD0mfVtVt9Pg+1M1Gb+VBuzH5DBKVC2eOBlIAIHzJ+Zuh6v56n0&#xA;OzKrpmnIlhpdyD8ErQ7OAem7khCPtAe4zXajCa4nvfZjtbFCZ052s+k955cP6u/5InMJ7x2KuxV2&#xA;Ksk8ieYjo+sosr0sbsiO4B6Kf2X/ANifwrlmOdF0/bOg/MYdh647j9I+P3vZ8zHzx2KuxV2KuxV2&#xA;KuxV2KuxV2KuxVL/ADBqg0vRru//AGoYyYwehdvhQf8ABEZGRoW5eh0/jZow7z9nX7HgruzuzuSz&#xA;MSWY9ST1OYL6eAAKDWKXYqvSaVEkRGISUBZF7EAhh+IxYmAJBPR7voVylzotjOhBEkEZ27HiKj6D&#xA;tmZE7PmetxmGacT0kfvfF/5v+T9R8see9St7pWNteTSXdhcHpJDM5Yb+KV4t7jNhikDHZ5rVxkMh&#xA;Mt73t9B+V/LLan+SPl6SV1F/Z2nrW8jN1jZ2IiLHxTiB7gZUJ1IuVLDx4h3gMJb4ahvh49a7UpmQ&#xA;6x5V5v1W3XVriaJrW6aXYF7ZJPTC0C8ZJOXKo6/DT+XxwU2XtSa+X7Dze0cVxHY3kPSjNdQwQMPe&#xA;2+ruR9ODdl6ev4+xkl15e1G9kklvpLpXY7JFeIYBuf8AdLWVG+k47rcPNCr5LjgmjuLaWH14JFkj&#xA;draFGIBqUd4gn/BAVxpiZjoybJNb0LR/LQsvy68w6nI5jv7zTbxY3jakkMYhelCPsuT8XttmPOdy&#xA;p2enw1Ak8yHx4CQQQaEbgjMh1oL1LyP57W+VNN1SQLeiiwTsaCXsFP8Al/r+ea3U6bh9UeT6R7O+&#xA;0YzVhzn95/DL+d5H+l9/v5zjMJ7N2KuxV2KvbfJGrNqfly1mduU8IMEx/wAqPYE+5WhzNxysPnHb&#xA;Gl8HUSA5HcfH9qfZN1bsVdirsVdirsVdirsVdirsVYT+a14Y9EtrUGhuJ6t7rGpNPvYZTnOz0nsz&#xA;ivNKX82P3vKsxXt3Yqk2s+b9A0jktzch51/49ovjkr4EDZf9kRl2PBKfIOo1/bml0u05XL+bHc/s&#xA;+NMQufzE1TUfXFhGthZwI0k1waSS8RsAKjgpckKNjuczYaOI57vGa72wz5NsIGOPfzl+ofI+9kX5&#xA;Lfns3lVW0TzGZbnRJZGkhulrJLbPIeT1Xq8bMeRpuDU71y7Jhvk87g1hB9Zu+r37V9L/AC+/M7y8&#xA;bdp7fVbT7UNzayKZrdyPtKwq0beKsN+4zHBMS58owyx7wmOk6ZpnlXyba6RdSG6srC3FrVoy7zLQ&#xA;gL6Shi7MNuIG+JlZtljwmqAJoPkfz15zuLjULmws/WEHrPHDDMp+sleRCLc7tzlA+Ejud2qczI8n&#xA;SZKMjSN8m+RTC6arrK87s0eC1bcRnqGfxf27fPpJrJZbqmn/AF+19D6zPakGqy28hjcH6Oo9jixS&#xA;6y8nWVpdetNd3d7IjVX15mK1B2+FeNfpxTae4oVNEgutZ8yW+gaX8V849W5noGS0twRynkBBBbei&#xA;IftMRXbITlQb8GLik9v8y2NtYeQdYs7ZeMMGmXSJU1Y0gerMx3ZmO7E7k75ijm7iQqJfBeZzoHAk&#xA;Go64Esw0T8ytZsUWK8Av4UFKueM1P9ffl/shX3zEyaOJ5bPVdn+1upw1HJ+9j5/V8+vxBPmzrRvP&#xA;Pl7VOKJP9WuG29CeiNXps1eLV9jXMLJp5x6PaaD2j0mpoCXBPult9vI/O/JkGUO9dir0X8pLw/7k&#xA;bIn4f3cyD33Vv+NcyMB5h5L2oxfRP3j9X6Xo2ZDyLsVdirsVdirsVdirsVdirsVecfm9J8elp0AE&#xA;7V+fAfwzHz9Hr/ZYbZD/AFf0vGtR86abBJJb2CNqV3GCzxwEemgHeWY/Aijue2HHpZS8g5HaPtPp&#xA;tPcYnxJ90eXxly+VvOte89eYNQkeIXAtrcGnp2xKg/N/tN99MzsemhHzeG7Q9o9VqduLgh3R2+Z5&#xA;n7vJjgBJAHc0G1f6ZkF0QCb6lcwWumx6VbkNMzCXUZV6GQCiRAjYiME18WxQUnwoXwXFxbzLNBI0&#xA;Mi7rJGxVgfYjcYGQJD7C/Kq1g81/k1pFvdTyPKolVrlyXcTRzOKnkfi6069Mwc8LNPSdka+WAjJH&#xA;fofNfffkR5V1k3N7ramTXLi2S2XUbdmQxtCxMdwo2rLx4KxatQvuauOUogBHaEcWbLKcY8Ik8p1j&#xA;TvNPki4Nl5rgebTweNn5hgRngkXsJqVMb/6349cy4zBdDlwSgd+S+31jSbleVveQSjoeMikg+BFc&#xA;nbTwlUvNU0y3Z2nu4YlDGpeRV7+5wWtFCae/mDzXcnTfJlo13ITwuNWkUrZ247lpGFGYdQBWvYHB&#xA;KYHNsx4ZTOz3v8ufy803yVo720MhvNSu2Eup6nIKSTy707miLU8Vrt8ycxZysu3xYhAUiPzJvRZf&#xA;l95kudqppt0Er05NCyr/AMMRggLITmlUCXwb39szXRgN4UOBIII6jAls0rt/nX5YhJHVPND85a9p&#xA;JSOC59S2FB6E9XjA6bftKB/knKcmnhPmN3b9n9varS0IyuH82W4/WPgQ9H03zzZSrEuqQtprzf3M&#xA;znnbSf6k4+H516ZgZNJKPLd7ns72s0+b05f3UvP6f9N0+Ne961+UsnLXrjiwMb2jMCNwf3kdDX6c&#xA;rw8272lo6eJH88fcXq+ZTxDsVdirsVdirsVdirsVdirsVeTf85A6RbXmmaXd31zLFptmbj63DGxR&#xA;JAyow9Tj8RAEbfCOuW4YgndxtXnyRhwxJEZcx391vmDXvMovIzYabCLHSEPw26AKZCOjy06nbp+v&#xA;Mp1CRYUOGxrUVYcaHw+1X/hciWY5F2SYO/zGAllEW7/Pt/CmIQTb64/5xin5/lwIeVfSuZTx7jm5&#xA;/XTMbNzdnoT6S9T1XVdO0nT59R1K4S1sbVDJPcSGiqo/z2HfKXMJp89/mD58/Mjz75d1e88padPp&#xA;/kawjZp9RasVxfRqaSFAfi9ILUkL2B5H9kXwAB35uHmnOUTw8nzxtyLEBmPUsA3665kU63iKpchT&#xA;cP8ACo4u1CAAevtgpJkXt/5WebPzT8meVLbXp9Nn1nyFLI4khVg89tGho08I+0I+XLY/Dsfs15ZT&#xA;kESa6udp5TjGzvF9HeWvM2h+ZdHg1fRbpLuxnHwyL1Vh1R1O6uvdTvlBFOfGQIsMV/PS6+r/AJY6&#xA;yOVPWiMVPHkCafhk8X1OPqz6C+KAaZluoBbdHR2RwQ6GjA+OAFlINZJg2SKBeQ23Cj/K2Nf+BH35&#xA;Hqz6NZJgnGg+ZLnSy0Eka3emzEfWLKUBkP8AlLWtGwJfRf8AzjzpenfX73VtImlGmS2oQWbsWjjk&#xA;eQE8QxJU1ibkKkHMfNEc+rtNFnyGJx2eDnXS+97llDmOxV2KuxV2KuxV2KuxV2KuxViH5saEda8i&#xA;alaL/eLGZENK0+Eox+hHY5ZiNFx9VG4HyfDTqyMUYUZSQwPUEZlunawodQeAJFCvKtKg13p92RLO&#xA;JcN+PwkFq7fa6f6tT+GNrw9yxasxY9BsorX5n2OAbllLYUqKpZgq7ljQD3OSa30v/wA4u6pFbaBr&#xA;cNzJxitZOZY1oqxbuRT/AIy5RmHJz9DLchnlx5KuvO+qRap5vRk8v2riTR/K7bKxHS41AA/HIf2Y&#xA;uiDZqksMpuuTmmHEd+TPlggSAQLGqwKvprEAAgQCnEL0pTamRbHwn+YujaRo3nvXdL06YvYWd28c&#xA;BValejNEan/dTMY69+NczIEkOlzwEZkBLdNsrG98w2dnc3Bt7a6vIoLm4Kj91HLKEeQAn4uIauSk&#xA;aDGEQZUX3xp+n2enafbafZxCKztIkggiHRY41Cqv3DMF3gFMLuvIVx5d1qbzJ5HRbeW5PLWfLxPC&#xA;0vgOrxfswXA/ZYfCejUqTkrvm18FGwxf/nIzWoLn8somgDAXUqyBJFKOvpuI3R1O6sjSUYdjluEb&#xA;uLrZekPlGRGjco2xU0OZDrUZqUBksLLUoxUEG2uewDxUA3JFKxlKe9ciWyO4pAnvQEnjyG1B/wAN&#xA;x/DDbHg73VrUkAEmu3WlAADsPDELIh2SYOxV9k/84++Xn0n8v7aSUFZbz94Q2xC7mn0O7jMXMd3a&#xA;6KFRvvemZS5jsVdirsVdirsVdirsVdirsVWyRpLG0cihkcFXU9CCKEYqQ+IPzg8pS+V/Pmo2JB+r&#xA;zt9atXP7UcpJrX/WrmbCVh0ebHwSIYXk2p2KurSppUDcitK03wFkBZcBxAWtabE1rv1O/wA8QFkb&#xA;K5Njy3HHcEbGvb8cUPe/+cXdURdavbbcGcy06ftqr/8AMg5VmGzl6OXr976YzFdqgdd1aDR9Ev8A&#xA;Vrj+40+3lupBWlVhQuR/wuEIJoW/P68vLi9u5ry5f1Lm5dp53IoTJKxdz/wTHM2IoOinKza24ANx&#xA;KpIAZmBJFaVJFcKLovuv8s/MR8x+QdC1lm5zXNoguW8Z4v3U3/JRGzCkKLvMcriCybIs3gX/ADlT&#xA;qfDTbKx/mCn/AJGOW/7F8ycA2dbrpbgeT5qldpG9Rt2b7XhX2+jLnBTjy+sd9a32jSfauk9a0rQn&#xA;14QSFWvd0LDEpBSSjUHKte5JrUjYmo9xiFlzdhYuxVPfI/lu58yea9N0a3TmbmZRIKGgjX4nLU6D&#xA;iOuRkaFs4RMiAH3hp9jBYWMFnAKQ26LGnjRRSp9zmETbvIxERQRGBk7FXYq7FXYq7FXYq7FXYq7F&#xA;XYq8t/Pz8sH84eXBf6bHy17SQ0lugG88PWSH/W/aT3275binRcXVYeMWOYfHrKysVYEMDQg7EEZl&#xA;OpawobWleor+yKkGo3qKeGAsg4Ab1NKYocSOg6frxV6x/wA49ahFaecF47NJJbLT2bnE5++XIZBs&#xA;W7AamD5vrzMN3Tzj/nITUXsvyo1hIm4zXxgs0JrT99Ogfp/xXyycBu05zUC+MmYsxY7kmp+nMx0q&#xA;+5/3ol/12/XipfVv/OLWpTXHkC8sZhxk0/UpkVPCOZEmG3b4nbMTKN3b6SVweyZW5L5g/wCcmtUD&#xA;67Fbn4hDNFwHtFFyYf8ABTZl4h6XUauV5D5PCgdiPHLHGXRyPDIksLlZEbkjCoZSpqDiq+7m9e5k&#xA;mKCN5DzkUbDkdjxWgoKjAGR5KOSYOxV9S/8AONv5Xy6LprebNXh4alqUYXT4XHxRWp35mvRpf+I0&#xA;8cxcs72dppMNDiPN7hlLmuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kvnv8APv8AI17p7nzd5Xg5XBrL&#xA;q2mRru/dp4VHVu7r36jetb8WToXA1Wmv1RfN2ZLrVr1A5AVKHlTepHcbdqdciWcO5Ufc8uobflvv&#xA;77+PXCxK3Chmf5WXn1fzKHQ8JBbt6Z8ZEkSQfcBgLIGt32/DKk0McyGqSKHU+IYVGYDvwbeN/wDO&#xA;Vd20X5e2ECGj3GqQ1AJB4RwzSE/QVGW4ubjas+h8oKaqD0qMyg6g81eVC926Dq0hA+lsV6vpj/nG&#xA;G5Av/PFh2tr6Bl+TetH1/wCeOY2Ybuz0R2L3fKXNfHH556mt55m5r8dZLqUH/iuVwifcFzNiKDo8&#xA;srkT5vMcm1LkWrdK03p/DbxwJU1PJmfqGNFNOw7gnfc9cAZy2FLsk1vc/wAiPyPm1e4g80eZ7Ypo&#xA;8dJdOsZBQ3LihWR1P+6R2B+3/q9aMmStg52m01+qXJ9RAACg6ZjOzdirsVdirsVdirsVdirsVdir&#xA;sVdirsVdirsVeLfm7/zj1Y+Y3m1vywI7HXHJkuLRvht7pjuTX/dch8fsk9abtl0MtbFw8+lEt483&#xA;zBrOi6ro2oTabq1pJZ3sBpLbzLxYeB91PYjY9syQQXWGJid+aBiGxiPUbrt1Hbt9GAMpjq3kmtP/&#xA;ACPOIfM1gztROboF8TLGyj8aYEvtvyTeC78qaZL3WERH5wkx/wDGmYeQVIu608rgHjv/ADljeqll&#xA;5XsXJ43FxdyFRXf04kSvht62Sxc2rWHYB8zKpVFB3+FT94rmUHVy5phYwifXreEiolukQgbfakAx&#xA;Xq+gf+cfJRb/AJmea7Has9qlyaAfszk7+/7/ADHzOw0R5vedYvPqWk3l53t4JJQPEqpIH35TEWXN&#xA;nKokviP8yblJfMJRWo0EEcTr41LSf8bDM50LEsKHS1EYUfak2G3Qd+3YZEs4BWsbG7vbmKysYJLm&#xA;6mISG3iUvI7dgqqKk/Rh5I3JfR/5Tf8AON8dm8Ot+dY1muVIkt9FqHjQjcG4IqHP+QPh8a9Mx55e&#xA;gdhg0lby+T39VVVCqAFAoANgAMoc9vFXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYqkHnDyJ5&#xA;W832P1PXbFLkKD6FwPgniJ7xyD4l+XQ9wclGRHJryYozFF86+e/+cXvM2ls955VnGs2a1ItJOMV2&#xA;F/l3pHJt3FD4Llwyg83CnpCOW4ePanpeo6bdPbahazWdyhpJb3EbRSKfdWAIy8G3BlEg0XaTdLa6&#xA;na3LAFYJY5TXwjcPt/wNMUB9n/lHf+rot1Zlqvaz8gPBJV2/4ZWzGzDd2ehlcSO549/zlteOfMfl&#xA;63Xf6pZz3NDWn72VU/5lYcSNYeTwSMu0aM3cUHyHwj9WXx5OvnzTzy3bNcebbONeq3PqfRETIfwX&#xA;Cxev/k5di2/5yA1CIbfXNNeGm25CQTf8y8pzcnO0R9T3H8zb8WvlSaMEh7uRIFI+fNv+FQjKsQuT&#xA;k6yVQ974u84XZufMd++3wzNH7/uqRf8AMuuZbqEstLS5u7hILaF7ieQhY4YkaR3Y9FVVqST7YkqB&#xA;b1jyR/zjT50110vNdpoNg1KJMA92yV7Qg/AT/wAWEEeByk5QHNhpJEb7B9GeR/yw8neS7fho1kPr&#xA;TCk2oT0kuX8ayUHEf5KAD2ymUyebnY8MYcmV5BtdirsVdirsVdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVdiqX6z5f0LW7b6trGn2+oQb0juYkkAr3XkDxPuMIJDGUBLmHmWv/APOMf5d6gzS6abrR&#xA;5q1UQSerDX3jmDmnsGGWDMXGlo4HlsyryT5E1Dyxeszagt9byQCKYlDE7SKQQ/Grjx798Z5OIJwa&#xA;c45Xezy/8/8A8qvzA82+cI9T0LTlvbGLTYrRCJ4Ym9QTySNVZpE/n64ccgBux1OKUjs8hH5H/mxA&#xA;gEnly5NSacWifb/YO1MtjkDiT08+5lPkX8nvP8XmCW/vNEngjhVzAZOKcnkPHap/lJw+JHvY/l8n&#xA;czfyV+Vvn/TPzhsvMtzpwh0aIyieYzwFisll6Ioiuz7S07ZVkmCHJ02CcZWQ9Z86eUr3zG9nEl0l&#xA;raW/JnqpdmdqD7NVHwgePfIQnwt+owHJW9Bg2j/84w+RoLg3WtXV3rNw7cpVdxbwsTufhi/ef8lM&#xA;kcxYx0URz3el6B5S8seXojFoml22nqwo7QRqruP8t/tt/sjlRkTzcmOOMeQTbAydirsVdirsVdir&#xA;sVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsV&#xA;dirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVd&#xA;irsVdirsVdirsVdirsVdirsVf//Z</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:193469d6-c108-0449-affe-66deafb35b86</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:193469d6-c108-0449-affe-66deafb35b86</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:dff2cf0f-695c-4eba-a773-7dc09d837a81</stRef:instanceID>
            <stRef:documentID>xmp.did:df60e01d-90e3-0c42-b5ca-d60fd320a32d</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:9055feea-6df3-418a-84b0-35eb367eec15</stEvt:instanceID>
                  <stEvt:when>2016-04-18T22:41:34+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 2015 (Macintosh)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:193469d6-c108-0449-affe-66deafb35b86</stEvt:instanceID>
                  <stEvt:when>2020-02-27T19:52:26+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator 24.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <illustrator:CreatorSubTool>Adobe Illustrator</illustrator:CreatorSubTool>
         <pdf:Producer>Adobe PDF library 15.00</pdf:Producer>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:HasVisibleTransparency>False</xmpTPg:HasVisibleTransparency>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>500.000000</stDim:w>
            <stDim:h>500.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Default Swatch Group</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Kulit</xmpG:groupName>
                  <xmpG:groupType>1</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=249 G=248 B=247</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>249</xmpG:red>
                           <xmpG:green>248</xmpG:green>
                           <xmpG:blue>247</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Color Group 2</xmpG:groupName>
                  <xmpG:groupType>1</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=250 G=101 B=100 1</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>101</xmpG:green>
                           <xmpG:blue>100</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Grays</xmpG:groupName>
                  <xmpG:groupType>1</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=100</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=90</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>65</xmpG:red>
                           <xmpG:green>64</xmpG:green>
                           <xmpG:blue>66</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=80</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>88</xmpG:red>
                           <xmpG:green>89</xmpG:green>
                           <xmpG:blue>91</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=70</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>109</xmpG:red>
                           <xmpG:green>110</xmpG:green>
                           <xmpG:blue>113</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=60</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>128</xmpG:red>
                           <xmpG:green>130</xmpG:green>
                           <xmpG:blue>133</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=50</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>147</xmpG:red>
                           <xmpG:green>149</xmpG:green>
                           <xmpG:blue>152</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=40</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>167</xmpG:red>
                           <xmpG:green>169</xmpG:green>
                           <xmpG:blue>172</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=30</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>188</xmpG:red>
                           <xmpG:green>190</xmpG:green>
                           <xmpG:blue>192</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=20</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>209</xmpG:red>
                           <xmpG:green>211</xmpG:green>
                           <xmpG:blue>212</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=10</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>230</xmpG:red>
                           <xmpG:green>231</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=0 M=0 Y=0 K=5</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>241</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>242</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Freepik logo</xmpG:groupName>
                  <xmpG:groupType>1</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=249 G=251 B=252</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>249</xmpG:red>
                           <xmpG:green>251</xmpG:green>
                           <xmpG:blue>252</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -500 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 500 li
500 500 li
500 0 li
cp
clp
500 500 mo
0 500 li
0 0 li
500 0 li
500 500 li
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.016907 .00112917 .00271611 0 cmyk
f
415.8 323.984 mo
415.8 326.604 415.688 329.206 415.481 331.788 cv
415.463 331.788 li
398.881 321.401 346.477 300.365 346.477 300.365 cv
346.477 300.365 328.286 284.868 317.692 270.42 cv
319.021 265.479 321.361 254.53 323.513 244.218 cv
323.813 242.795 324.112 241.374 324.393 239.988 cv
325.16 239.876 325.89 239.726 326.601 239.577 cv
337.98 237.125 342.734 230.256 344.175 220.206 cv
387.446 242.889 415.8 280.919 415.8 323.984 cv
cp
0 .740505 .350195 0 cmyk
f
389.523 323.235 mo
390.74 324.171 410.223 339.181 411.14 341.988 cv
412.075 344.796 404.196 370.904 399.573 378.465 cv
399.442 378.689 399.292 378.914 399.162 379.102 cv
384.47 402.084 360.851 421.155 331.767 433.657 cv
346.065 417.487 353.458 390.836 353.458 390.836 cv
344.456 358.964 li
365.062 354.19 li
369.329 330.946 li
364.688 318.163 353.982 309.292 353.982 309.292 cv
351.718 306.223 349.191 303.172 346.477 300.365 cv
346.477 300.365 398.881 321.401 415.463 331.788 cv
415.481 331.788 li
415.388 333.136 415.238 334.446 415.07 335.774 cv
389.523 323.235 li
cp
.924742 .658564 .530907 .479438 cmyk
f
389.523 323.235 mo
415.07 335.774 li
413.236 350.953 407.864 365.346 399.573 378.465 cv
404.196 370.904 412.075 344.796 411.14 341.988 cv
410.223 339.181 390.74 324.171 389.523 323.235 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
373.353 146.017 mo
386.117 149.91 401.782 160.99 401.146 173.473 cv
401.146 173.473 398.039 167.147 389.785 158.5 cv
382.056 150.396 374.289 146.485 373.353 146.017 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
401.146 173.473 mo
401.782 160.99 386.117 149.91 373.353 146.017 cv
373.315 145.999 373.296 145.999 373.259 145.98 cv
373.259 145.98 373.296 145.999 373.353 146.017 cv
374.289 146.485 382.056 150.396 389.785 158.5 cv
398.039 167.147 401.146 173.473 401.146 173.473 cv
cp
383.74 197.803 mo
382.355 198.458 337.232 219.551 250.84 219.551 cv
250.84 183.037 li
279.625 183.037 306.257 178.826 322.858 171.938 cv
338.729 165.351 346.346 159.287 346.477 156.966 cv
346.608 154.626 335.847 152.792 335.847 152.792 cv
335.678 152.811 li
335.098 148.207 334.312 143.248 333.451 138.101 cv
333.339 137.483 333.227 136.847 333.114 136.21 cv
335.547 136.154 li
406.498 140.833 413.854 171.808 399.143 189.999 cv
390.459 200.742 375.842 211.484 344.381 218.484 cv
343.614 218.652 342.828 218.821 342.042 218.989 cv
337.718 219.907 333.096 220.749 328.136 221.516 cv
308.279 224.585 283.069 226.401 250.84 226.401 cv
250.84 223.107 li
329.39 223.107 363.995 210.979 383.74 197.803 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
361.375 350.467 mo
361.394 350.467 li
364.725 338.395 366.578 330.179 366.578 330.179 cv
366.578 330.179 361.73 319.754 353.982 309.292 cv
353.982 309.292 364.688 318.163 369.329 330.946 cv
365.062 354.19 li
344.456 358.964 li
353.458 390.836 li
353.458 390.836 346.065 417.487 331.767 433.657 cv
329.353 434.705 326.901 435.697 324.412 436.633 cv
334.406 425.348 343.295 405.396 350.295 385.802 cv
350.164 385.483 338.86 357.354 338.86 357.354 cv
345.485 355.332 li
361.375 350.467 li
cp
.912901 .670741 .584283 .647578 cmyk
f
346.458 385.839 mo
346.103 386.082 340.544 389.619 327.574 382.882 cv
313.818 375.732 313.818 344.122 313.818 344.122 cv
314.081 371.578 307.006 420.65 297.91 444.625 cv
297.386 444.756 296.843 444.868 296.301 444.98 cv
300.999 424.88 308.41 365.926 310.394 321.289 cv
311.067 306.372 311.124 293.065 310.263 283.334 cv
314.174 277.888 316.944 273.228 317.692 270.42 cv
328.286 284.868 346.477 300.365 346.477 300.365 cv
349.191 303.172 351.718 306.223 353.982 309.292 cv
361.73 319.754 366.578 330.179 366.578 330.179 cv
366.578 330.179 364.725 338.395 361.394 350.467 cv
361.375 350.467 li
345.485 355.332 li
319.808 318.22 li
320.519 319.474 331.991 339.648 338.86 357.354 cv
338.86 357.354 350.164 385.483 350.295 385.802 cv
343.295 405.396 334.406 425.348 324.412 436.633 cv
324.393 436.651 li
322.877 437.232 321.323 437.794 319.77 438.336 cv
328.286 426.34 338.354 409.065 346.458 385.839 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
250.84 219.551 mo
337.232 219.551 382.355 198.458 383.74 197.803 cv
363.995 210.979 329.39 223.107 250.84 223.107 cv
250.84 219.551 li
cp
.912901 .670741 .584283 .647578 cmyk
f
335.847 152.792 mo
335.847 152.792 346.608 154.626 346.477 156.966 cv
346.346 159.287 338.729 165.351 322.858 171.938 cv
306.257 178.826 279.625 183.037 250.84 183.037 cv
250.84 179.537 li
316.663 179.537 336.408 159.417 336.408 159.417 cv
336.221 157.303 335.978 155.094 335.678 152.811 cv
335.847 152.792 li
cp
f
319.808 318.22 mo
345.485 355.332 li
338.86 357.354 li
331.991 339.648 320.519 319.474 319.808 318.22 cv
cp
f
344.175 220.206 mo
342.734 230.256 337.98 237.125 326.601 239.577 cv
326.601 239.577 335.304 232.839 337.325 230.275 cv
342.042 224.286 342.042 218.989 342.042 218.989 cv
342.828 218.821 343.614 218.652 344.381 218.484 cv
344.325 219.064 344.25 219.645 344.175 220.206 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
327.574 382.882 mo
340.544 389.619 346.103 386.082 346.458 385.839 cv
338.354 409.065 328.286 426.34 319.77 438.336 cv
312.751 440.807 305.434 442.922 297.91 444.625 cv
307.006 420.65 314.081 371.578 313.818 344.122 cv
313.818 344.122 313.818 375.732 327.574 382.882 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
342.042 218.989 mo
342.042 218.989 342.042 224.286 337.325 230.275 cv
335.304 232.839 326.601 239.577 326.601 239.577 cv
325.89 239.726 325.16 239.876 324.393 239.988 cv
325.759 233.438 326.938 227.58 327.574 224.286 cv
327.93 222.526 328.136 221.516 328.136 221.516 cv
333.096 220.749 337.718 219.907 342.042 218.989 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
260.385 145.812 mo
301.597 144.408 325.534 134.227 332.179 130.97 cv
332.384 132.112 332.59 133.234 332.796 134.357 cv
325.441 138.831 314.679 142.05 301.691 144.576 cv
301.691 144.576 315.372 147.533 333.451 138.101 cv
334.312 143.248 335.098 148.207 335.678 152.811 cv
335.978 155.094 336.221 157.303 336.408 159.417 cv
336.408 159.417 316.663 179.537 250.84 179.537 cv
250.84 145.98 li
254.116 145.98 257.297 145.924 260.385 145.812 cv
cp
.816892 .52462 .462013 .225162 cmyk
f
333.114 136.21 mo
333.227 136.847 333.339 137.483 333.451 138.101 cv
315.372 147.533 301.691 144.576 301.691 144.576 cv
314.679 142.05 325.441 138.831 332.796 134.357 cv
332.908 134.975 333.002 135.593 333.114 136.21 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
331.355 126.497 mo
331.636 127.994 331.917 129.491 332.179 130.97 cv
325.534 134.227 301.597 144.408 260.385 145.812 cv
304.18 141.338 320.425 132.449 331.355 126.497 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
322.353 72.6709 mo
323.7 87.606 327.836 107.632 331.355 126.497 cv
320.425 132.449 304.18 141.338 260.385 145.812 cv
257.297 145.924 254.116 145.98 250.84 145.98 cv
250.84 72.0908 li
280.467 72.0908 282.582 54.6665 282.582 54.6665 cv
276.369 59.102 269.818 62.9951 250.84 62.9951 cv
250.84 60.6743 li
254.771 60.8799 260.685 60.7305 267.479 58.7275 cv
275.246 56.4629 280.842 52.7764 284.135 50.2119 cv
289.657 50.0063 296.544 50.2495 309.944 59.8882 cv
315.84 64.1367 319.789 68.9277 322.353 72.6709 cv
cp
304.105 61.6289 mo
304.648 62.1152 313.257 69.8262 315.316 77.874 cv
317.431 86.2021 323.251 114.856 323.251 114.856 cv
319.77 75.7588 li
319.77 75.7588 316.851 68.4038 304.105 61.6289 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
328.136 221.516 mo
328.136 221.516 327.93 222.526 327.574 224.286 cv
327.574 224.286 322.989 235.627 319.564 246.445 cv
315.297 248.392 310.955 248.542 298.846 248.766 cv
283.162 249.047 278.708 252.153 272.513 252.341 cv
266.318 252.527 259.543 245.172 250.84 245.172 cv
250.84 226.401 li
283.069 226.401 308.279 224.585 328.136 221.516 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
327.574 224.286 mo
326.938 227.58 325.759 233.438 324.393 239.988 cv
324.112 241.374 323.813 242.795 323.513 244.218 cv
322.072 245.154 320.837 245.884 319.564 246.445 cv
322.989 235.627 327.574 224.286 327.574 224.286 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
319.77 75.7588 mo
323.251 114.856 li
323.251 114.856 317.431 86.2021 315.316 77.874 cv
313.257 69.8262 304.648 62.1152 304.105 61.6289 cv
316.851 68.4038 319.77 75.7588 319.77 75.7588 cv
cp
.816892 .52462 .462013 .225162 cmyk
f
319.564 246.445 mo
320.837 245.884 322.072 245.154 323.513 244.218 cv
321.361 254.53 319.021 265.479 317.692 270.42 cv
316.944 273.228 314.174 277.888 310.263 283.334 cv
307.605 287.039 304.423 291.101 300.999 295.199 cv
294.542 302.892 287.205 310.715 280.748 316.441 cv
281.815 315.375 291.771 305.362 300.399 291.83 cv
309.533 277.532 314.155 269.971 316.476 257.955 cv
317.112 254.624 318.254 250.582 319.564 246.445 cv
cp
.016907 .00112917 .00271611 0 cmyk
f
269.462 260.987 mo
275.452 262.484 282.526 272.441 283.518 280.302 cv
283.63 281.762 283.63 282.679 283.63 282.679 cv
283.667 281.911 283.63 281.125 283.518 280.302 cv
283.331 277.382 282.825 272.311 281.44 268.343 cv
279.681 263.346 271.858 261.455 269.462 260.987 cv
cp
263.998 326.286 mo
258.776 327.521 253.349 327.727 250.84 327.727 cv
250.84 300.608 li
257.84 300.608 266.168 295.536 266.692 295.218 cv
266.3 295.256 261.471 295.836 250.84 295.836 cv
250.84 288.219 li
264.915 288.219 278.333 286.291 278.333 286.291 cv
271.109 286.16 259.618 283.184 257.803 283.334 cv
256.006 283.483 250.84 286.927 250.84 286.927 cv
250.84 271.843 li
256.717 271.843 259.375 269.503 260.573 267.332 cv
267.946 265.76 268.751 260.856 268.751 260.856 cv
267.479 262.597 265.851 263.869 261.546 263.701 cv
261.546 263.701 257.672 269.129 250.84 269.129 cv
250.84 245.172 li
259.543 245.172 266.318 252.527 272.513 252.341 cv
278.708 252.153 283.162 249.047 298.846 248.766 cv
310.955 248.542 315.297 248.392 319.564 246.445 cv
318.254 250.582 317.112 254.624 316.476 257.955 cv
314.155 269.971 309.533 277.532 300.399 291.83 cv
291.771 305.362 281.815 315.375 280.748 316.441 cv
280.692 316.479 280.654 316.517 280.654 316.517 cv
276.5 320.203 272.738 323.011 269.818 324.358 cv
268.04 325.182 266.038 325.818 263.998 326.286 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
310.263 283.334 mo
311.124 293.065 311.067 306.372 310.394 321.289 cv
309.664 326.136 308.747 330.759 307.773 334.988 cv
304.536 349.063 300.662 358.964 300.662 358.964 cv
293.381 348.838 282.975 339.948 275.096 333.997 cv
272.158 331.788 269.575 329.973 267.647 328.682 cv
282.096 324.134 290.255 312.829 300.999 295.199 cv
304.423 291.101 307.605 287.039 310.263 283.334 cv
cp
.016907 .00112917 .00271611 0 cmyk
f
307.773 334.988 mo
308.747 330.759 309.664 326.136 310.394 321.289 cv
308.41 365.926 300.999 424.88 296.301 444.98 cv
293.288 445.636 290.237 446.234 287.167 446.758 cv
296.769 420.051 307.455 337.571 307.773 334.988 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
300.662 358.964 mo
300.662 358.964 304.536 349.063 307.773 334.988 cv
307.455 337.571 296.769 420.051 287.167 446.758 cv
283.986 447.301 280.785 447.788 277.529 448.181 cv
277.529 448.181 273.299 405.809 271.952 389.807 cv
270.585 373.805 262.968 353.273 262.968 353.273 cv
269.968 340.117 li
271.952 337.759 li
271.952 337.759 289.563 348.576 300.662 358.964 cv
cp
.016907 .00112917 .00271611 0 cmyk
f
280.748 316.441 mo
287.205 310.715 294.542 302.892 300.999 295.199 cv
290.255 312.829 282.096 324.134 267.647 328.682 cv
265.514 327.24 264.185 326.398 263.998 326.286 cv
266.038 325.818 268.04 325.182 269.818 324.358 cv
272.738 323.011 276.5 320.203 280.654 316.517 cv
280.654 316.517 280.692 316.479 280.748 316.441 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
281.44 268.343 mo
282.825 272.311 283.331 277.382 283.518 280.302 cv
282.526 272.441 275.452 262.484 269.462 260.987 cv
271.858 261.455 279.681 263.346 281.44 268.343 cv
cp
f
300.662 358.964 mo
289.563 348.576 271.952 337.759 271.952 337.759 cv
275.096 333.997 li
282.975 339.948 293.381 348.838 300.662 358.964 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
282.582 54.6665 mo
282.582 54.6665 280.467 72.0908 250.84 72.0908 cv
250.84 62.9951 li
269.818 62.9951 276.369 59.102 282.582 54.6665 cv
cp
f
277.529 448.181 mo
275.882 448.387 274.235 448.573 272.569 448.742 cv
272.569 448.742 270.455 403.356 267.872 385.296 cv
265.289 367.217 259.356 352.769 259.356 352.769 cv
269.968 340.117 li
262.968 353.273 li
262.968 353.273 270.585 373.805 271.952 389.807 cv
273.299 405.809 277.529 448.181 277.529 448.181 cv
cp
f
275.096 333.997 mo
271.952 337.759 li
269.968 340.117 li
259.356 352.769 li
250.84 352.769 li
250.84 330.946 li
257.354 330.946 262.856 330.179 267.647 328.682 cv
269.575 329.973 272.158 331.788 275.096 333.997 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
272.569 448.742 mo
271.353 448.873 270.136 448.985 268.92 449.061 cv
268.901 448.742 li
269.949 429.951 265.963 388.646 265.963 388.646 cv
265.963 388.646 265.551 439.347 258.42 449.696 cv
255.913 449.79 253.386 449.827 250.84 449.827 cv
250.84 352.769 li
259.356 352.769 li
259.356 352.769 265.289 367.217 267.872 385.296 cv
270.455 403.356 272.569 448.742 272.569 448.742 cv
cp
f
278.333 286.291 mo
278.333 286.291 264.915 288.219 250.84 288.219 cv
250.84 286.927 li
250.84 286.927 256.006 283.483 257.803 283.334 cv
259.618 283.184 271.109 286.16 278.333 286.291 cv
cp
f
265.963 388.646 mo
265.963 388.646 269.949 429.951 268.901 448.742 cv
268.92 449.061 li
265.458 449.359 261.958 449.584 258.42 449.696 cv
265.551 439.347 265.963 388.646 265.963 388.646 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
268.751 260.856 mo
268.751 260.856 267.946 265.76 260.573 267.332 cv
261.602 265.441 261.546 263.701 261.546 263.701 cv
265.851 263.869 267.479 262.597 268.751 260.856 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
263.998 326.286 mo
264.185 326.398 265.514 327.24 267.647 328.682 cv
262.856 330.179 257.354 330.946 250.84 330.946 cv
250.84 327.727 li
253.349 327.727 258.776 327.521 263.998 326.286 cv
cp
f
250.84 295.836 mo
261.471 295.836 266.3 295.256 266.692 295.218 cv
266.168 295.536 257.84 300.608 250.84 300.608 cv
250.84 295.836 li
cp
.599145 .271214 .303471 .00570687 cmyk
f
260.573 267.332 mo
259.375 269.503 256.717 271.843 250.84 271.843 cv
250.84 269.129 li
257.672 269.129 261.546 263.701 261.546 263.701 cv
261.546 263.701 261.602 265.441 260.573 267.332 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
85.8745 323.984 mo
85.8745 326.604 85.9868 329.206 86.1929 331.788 cv
86.2114 331.788 li
102.793 321.401 155.197 300.365 155.197 300.365 cv
155.197 300.365 173.389 284.868 183.982 270.42 cv
182.653 265.479 180.313 254.53 178.162 244.218 cv
177.862 242.795 177.563 241.374 177.282 239.988 cv
176.514 239.876 175.785 239.726 175.073 239.577 cv
163.694 237.125 158.94 230.256 157.5 220.206 cv
114.229 242.889 85.8745 280.919 85.8745 323.984 cv
cp
0 .740505 .350195 0 cmyk
f
112.151 323.235 mo
110.935 324.171 91.4521 339.181 90.5347 341.988 cv
89.5991 344.796 97.4785 370.904 102.101 378.465 cv
102.232 378.689 102.382 378.914 102.513 379.102 cv
117.205 402.084 140.824 421.155 169.908 433.657 cv
155.609 417.487 148.216 390.836 148.216 390.836 cv
157.219 358.964 li
136.613 354.19 li
132.346 330.946 li
136.987 318.163 147.692 309.292 147.692 309.292 cv
149.957 306.223 152.483 303.172 155.197 300.365 cv
155.197 300.365 102.793 321.401 86.2114 331.788 cv
86.1929 331.788 li
86.2866 333.136 86.436 334.446 86.6045 335.774 cv
112.151 323.235 li
cp
.924742 .658564 .530907 .479438 cmyk
f
112.151 323.235 mo
86.6045 335.774 li
88.439 350.953 93.8101 365.346 102.101 378.465 cv
97.4785 370.904 89.5991 344.796 90.5347 341.988 cv
91.4521 339.181 110.935 324.171 112.151 323.235 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
128.322 146.017 mo
115.558 149.91 99.8926 160.99 100.529 173.473 cv
100.529 173.473 103.636 167.147 111.889 158.5 cv
119.619 150.396 127.386 146.485 128.322 146.017 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
100.529 173.473 mo
99.8926 160.99 115.558 149.91 128.322 146.017 cv
128.359 145.999 128.378 145.999 128.416 145.98 cv
128.416 145.98 128.378 145.999 128.322 146.017 cv
127.386 146.485 119.619 150.396 111.889 158.5 cv
103.636 167.147 100.529 173.473 100.529 173.473 cv
cp
168.224 138.101 mo
168.336 137.483 168.448 136.847 168.56 136.21 cv
166.127 136.154 li
95.1763 140.833 87.8213 171.808 102.532 189.999 cv
111.216 200.742 125.833 211.484 157.293 218.484 cv
158.061 218.652 158.847 218.821 159.633 218.989 cv
163.956 219.907 168.579 220.749 173.539 221.516 cv
193.396 224.585 218.606 226.401 250.834 226.401 cv
250.834 223.107 li
172.285 223.107 137.68 210.979 117.935 197.803 cv
119.319 198.458 164.443 219.551 250.834 219.551 cv
250.834 183.037 li
222.049 183.037 195.417 178.826 178.816 171.938 cv
162.946 165.351 155.328 159.287 155.197 156.966 cv
155.066 154.626 165.828 152.792 165.828 152.792 cv
165.996 152.811 li
166.576 148.207 167.362 143.248 168.224 138.101 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
140.3 350.467 mo
140.281 350.467 li
136.95 338.395 135.097 330.179 135.097 330.179 cv
135.097 330.179 139.944 319.754 147.692 309.292 cv
147.692 309.292 136.987 318.163 132.346 330.946 cv
136.613 354.19 li
157.219 358.964 li
148.216 390.836 li
148.216 390.836 155.609 417.487 169.908 433.657 cv
172.322 434.705 174.774 435.697 177.263 436.633 cv
167.269 425.348 158.379 405.396 151.379 385.802 cv
151.51 385.483 162.814 357.354 162.814 357.354 cv
156.189 355.332 li
140.3 350.467 li
cp
.912901 .670741 .584283 .647578 cmyk
f
181.867 318.22 mo
181.156 319.474 169.683 339.648 162.814 357.354 cv
162.814 357.354 151.51 385.483 151.379 385.802 cv
158.379 405.396 167.269 425.348 177.263 436.633 cv
177.282 436.651 li
178.798 437.232 180.351 437.794 181.904 438.336 cv
173.389 426.34 163.32 409.065 155.216 385.839 cv
155.572 386.082 161.13 389.619 174.1 382.882 cv
187.856 375.732 187.856 344.122 187.856 344.122 cv
187.594 371.578 194.668 420.65 203.764 444.625 cv
204.288 444.756 204.831 444.868 205.374 444.98 cv
200.676 424.88 193.265 365.926 191.281 321.289 cv
190.607 306.372 190.551 293.065 191.412 283.334 cv
187.5 277.888 184.73 273.228 183.982 270.42 cv
173.389 284.868 155.197 300.365 155.197 300.365 cv
152.483 303.172 149.957 306.223 147.692 309.292 cv
139.944 319.754 135.097 330.179 135.097 330.179 cv
135.097 330.179 136.95 338.395 140.281 350.467 cv
140.3 350.467 li
156.189 355.332 li
181.867 318.22 li
cp
.924742 .658564 .530907 .479438 cmyk
f
250.834 219.551 mo
164.443 219.551 119.319 198.458 117.935 197.803 cv
137.68 210.979 172.285 223.107 250.834 223.107 cv
250.834 219.551 li
cp
.912901 .670741 .584283 .647578 cmyk
f
165.828 152.792 mo
165.828 152.792 155.066 154.626 155.197 156.966 cv
155.328 159.287 162.946 165.351 178.816 171.938 cv
195.417 178.826 222.049 183.037 250.834 183.037 cv
250.834 179.537 li
185.011 179.537 165.266 159.417 165.266 159.417 cv
165.454 157.303 165.697 155.094 165.996 152.811 cv
165.828 152.792 li
cp
f
181.867 318.22 mo
156.189 355.332 li
162.814 357.354 li
169.683 339.648 181.156 319.474 181.867 318.22 cv
cp
f
157.5 220.206 mo
158.94 230.256 163.694 237.125 175.073 239.577 cv
175.073 239.577 166.371 232.839 164.349 230.275 cv
159.633 224.286 159.633 218.989 159.633 218.989 cv
158.847 218.821 158.061 218.652 157.293 218.484 cv
157.35 219.064 157.424 219.645 157.5 220.206 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
174.1 382.882 mo
161.13 389.619 155.572 386.082 155.216 385.839 cv
163.32 409.065 173.389 426.34 181.904 438.336 cv
188.923 440.807 196.241 442.922 203.764 444.625 cv
194.668 420.65 187.594 371.578 187.856 344.122 cv
187.856 344.122 187.856 375.732 174.1 382.882 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
159.633 218.989 mo
159.633 218.989 159.633 224.286 164.349 230.275 cv
166.371 232.839 175.073 239.577 175.073 239.577 cv
175.785 239.726 176.514 239.876 177.282 239.988 cv
175.916 233.438 174.736 227.58 174.1 224.286 cv
173.745 222.526 173.539 221.516 173.539 221.516 cv
168.579 220.749 163.956 219.907 159.633 218.989 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
241.289 145.812 mo
200.077 144.408 176.14 134.227 169.496 130.97 cv
169.29 132.112 169.084 133.234 168.878 134.357 cv
176.234 138.831 186.995 142.05 199.984 144.576 cv
199.984 144.576 186.303 147.533 168.224 138.101 cv
167.362 143.248 166.576 148.207 165.996 152.811 cv
165.697 155.094 165.454 157.303 165.266 159.417 cv
165.266 159.417 185.011 179.537 250.834 179.537 cv
250.834 145.98 li
247.559 145.98 244.377 145.924 241.289 145.812 cv
cp
.816892 .52462 .462013 .225162 cmyk
f
168.56 136.21 mo
168.448 136.847 168.336 137.483 168.224 138.101 cv
186.303 147.533 199.984 144.576 199.984 144.576 cv
186.995 142.05 176.234 138.831 168.878 134.357 cv
168.766 134.975 168.672 135.593 168.56 136.21 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
170.319 126.497 mo
170.039 127.994 169.758 129.491 169.496 130.97 cv
176.14 134.227 200.077 144.408 241.289 145.812 cv
197.495 141.338 181.25 132.449 170.319 126.497 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
179.322 72.6709 mo
177.974 87.606 173.838 107.632 170.319 126.497 cv
181.25 132.449 197.495 141.338 241.289 145.812 cv
244.377 145.924 247.559 145.98 250.834 145.98 cv
250.834 72.0908 li
221.207 72.0908 219.092 54.6665 219.092 54.6665 cv
225.306 59.102 231.856 62.9951 250.834 62.9951 cv
250.834 60.6743 li
246.904 60.8799 240.99 60.7305 234.196 58.7275 cv
226.429 56.4629 220.833 52.7764 217.539 50.2119 cv
212.018 50.0063 205.13 50.2495 191.73 59.8882 cv
185.835 64.1367 181.886 68.9277 179.322 72.6709 cv
cp
197.569 61.6289 mo
197.027 62.1152 188.417 69.8262 186.359 77.874 cv
184.244 86.2021 178.423 114.856 178.423 114.856 cv
181.904 75.7588 li
181.904 75.7588 184.824 68.4038 197.569 61.6289 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
173.539 221.516 mo
173.539 221.516 173.745 222.526 174.1 224.286 cv
174.1 224.286 178.686 235.627 182.11 246.445 cv
186.377 248.392 190.72 248.542 202.829 248.766 cv
218.512 249.047 222.966 252.153 229.162 252.341 cv
235.356 252.527 242.131 245.172 250.834 245.172 cv
250.834 226.401 li
218.606 226.401 193.396 224.585 173.539 221.516 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
174.1 224.286 mo
174.736 227.58 175.916 233.438 177.282 239.988 cv
177.563 241.374 177.862 242.795 178.162 244.218 cv
179.603 245.154 180.838 245.884 182.11 246.445 cv
178.686 235.627 174.1 224.286 174.1 224.286 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
181.904 75.7588 mo
178.423 114.856 li
178.423 114.856 184.244 86.2021 186.359 77.874 cv
188.417 69.8262 197.027 62.1152 197.569 61.6289 cv
184.824 68.4038 181.904 75.7588 181.904 75.7588 cv
cp
.816892 .52462 .462013 .225162 cmyk
f
182.11 246.445 mo
180.838 245.884 179.603 245.154 178.162 244.218 cv
180.313 254.53 182.653 265.479 183.982 270.42 cv
184.73 273.228 187.5 277.888 191.412 283.334 cv
194.07 287.039 197.251 291.101 200.676 295.199 cv
207.133 302.892 214.47 310.715 220.926 316.441 cv
219.86 315.375 209.903 305.362 201.275 291.83 cv
192.142 277.532 187.519 269.971 185.198 257.955 cv
184.562 254.624 183.42 250.582 182.11 246.445 cv
cp
.016907 .00112917 .00271611 0 cmyk
f
237.677 326.286 mo
242.898 327.521 248.326 327.727 250.834 327.727 cv
250.834 300.608 li
243.834 300.608 235.506 295.536 234.982 295.218 cv
235.375 295.256 240.204 295.836 250.834 295.836 cv
250.834 288.219 li
236.76 288.219 223.341 286.291 223.341 286.291 cv
230.565 286.16 242.056 283.184 243.872 283.334 cv
245.668 283.483 250.834 286.927 250.834 286.927 cv
250.834 271.843 li
244.958 271.843 242.3 269.503 241.102 267.332 cv
233.728 265.76 232.923 260.856 232.923 260.856 cv
234.196 262.597 235.824 263.869 240.129 263.701 cv
240.129 263.701 244.003 269.129 250.834 269.129 cv
250.834 245.172 li
242.131 245.172 235.356 252.527 229.162 252.341 cv
222.966 252.153 218.512 249.047 202.829 248.766 cv
190.72 248.542 186.377 248.392 182.11 246.445 cv
183.42 250.582 184.562 254.624 185.198 257.955 cv
187.519 269.971 192.142 277.532 201.275 291.83 cv
209.903 305.362 219.86 315.375 220.926 316.441 cv
220.983 316.479 221.02 316.517 221.02 316.517 cv
225.175 320.203 228.937 323.011 231.856 324.358 cv
233.634 325.182 235.637 325.818 237.677 326.286 cv
cp
232.212 260.987 mo
226.223 262.484 219.148 272.441 218.157 280.302 cv
218.044 281.762 218.044 282.679 218.044 282.679 cv
218.007 281.911 218.044 281.125 218.157 280.302 cv
218.344 277.382 218.849 272.311 220.234 268.343 cv
221.993 263.346 229.816 261.455 232.212 260.987 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
191.412 283.334 mo
190.551 293.065 190.607 306.372 191.281 321.289 cv
192.011 326.136 192.928 330.759 193.901 334.988 cv
197.139 349.063 201.013 358.964 201.013 358.964 cv
208.293 348.838 218.699 339.948 226.579 333.997 cv
229.517 331.788 232.1 329.973 234.027 328.682 cv
219.579 324.134 211.419 312.829 200.676 295.199 cv
197.251 291.101 194.07 287.039 191.412 283.334 cv
cp
.016907 .00112917 .00271611 0 cmyk
f
193.901 334.988 mo
192.928 330.759 192.011 326.136 191.281 321.289 cv
193.265 365.926 200.676 424.88 205.374 444.98 cv
208.387 445.636 211.438 446.234 214.507 446.758 cv
204.906 420.051 194.219 337.571 193.901 334.988 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
201.013 358.964 mo
201.013 358.964 197.139 349.063 193.901 334.988 cv
194.219 337.571 204.906 420.051 214.507 446.758 cv
217.688 447.301 220.889 447.788 224.146 448.181 cv
224.146 448.181 228.375 405.809 229.723 389.807 cv
231.089 373.805 238.706 353.273 238.706 353.273 cv
231.707 340.117 li
229.723 337.759 li
229.723 337.759 212.111 348.576 201.013 358.964 cv
cp
.016907 .00112917 .00271611 0 cmyk
f
220.926 316.441 mo
214.47 310.715 207.133 302.892 200.676 295.199 cv
211.419 312.829 219.579 324.134 234.027 328.682 cv
236.161 327.24 237.49 326.398 237.677 326.286 cv
235.637 325.818 233.634 325.182 231.856 324.358 cv
228.937 323.011 225.175 320.203 221.02 316.517 cv
221.02 316.517 220.983 316.479 220.926 316.441 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
220.234 268.343 mo
218.849 272.311 218.344 277.382 218.157 280.302 cv
219.148 272.441 226.223 262.484 232.212 260.987 cv
229.816 261.455 221.993 263.346 220.234 268.343 cv
cp
f
201.013 358.964 mo
212.111 348.576 229.723 337.759 229.723 337.759 cv
226.579 333.997 li
218.699 339.948 208.293 348.838 201.013 358.964 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
219.092 54.6665 mo
219.092 54.6665 221.207 72.0908 250.834 72.0908 cv
250.834 62.9951 li
231.856 62.9951 225.306 59.102 219.092 54.6665 cv
cp
f
224.146 448.181 mo
225.792 448.387 227.439 448.573 229.105 448.742 cv
229.105 448.742 231.22 403.356 233.803 385.296 cv
236.386 367.217 242.318 352.769 242.318 352.769 cv
231.707 340.117 li
238.706 353.273 li
238.706 353.273 231.089 373.805 229.723 389.807 cv
228.375 405.809 224.146 448.181 224.146 448.181 cv
cp
f
226.579 333.997 mo
229.723 337.759 li
231.707 340.117 li
242.318 352.769 li
250.834 352.769 li
250.834 330.946 li
244.321 330.946 238.818 330.179 234.027 328.682 cv
232.1 329.973 229.517 331.788 226.579 333.997 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
229.105 448.742 mo
230.322 448.873 231.538 448.985 232.755 449.061 cv
232.773 448.742 li
231.726 429.951 235.712 388.646 235.712 388.646 cv
235.712 388.646 236.124 439.347 243.254 449.696 cv
245.762 449.79 248.289 449.827 250.834 449.827 cv
250.834 352.769 li
242.318 352.769 li
242.318 352.769 236.386 367.217 233.803 385.296 cv
231.22 403.356 229.105 448.742 229.105 448.742 cv
cp
f
223.341 286.291 mo
223.341 286.291 236.76 288.219 250.834 288.219 cv
250.834 286.927 li
250.834 286.927 245.668 283.483 243.872 283.334 cv
242.056 283.184 230.565 286.16 223.341 286.291 cv
cp
f
235.712 388.646 mo
235.712 388.646 231.726 429.951 232.773 448.742 cv
232.755 449.061 li
236.217 449.359 239.717 449.584 243.254 449.696 cv
236.124 439.347 235.712 388.646 235.712 388.646 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
232.923 260.856 mo
232.923 260.856 233.728 265.76 241.102 267.332 cv
240.073 265.441 240.129 263.701 240.129 263.701 cv
235.824 263.869 234.196 262.597 232.923 260.856 cv
cp
f
237.677 326.286 mo
237.49 326.398 236.161 327.24 234.027 328.682 cv
238.818 330.179 244.321 330.946 250.834 330.946 cv
250.834 327.727 li
248.326 327.727 242.898 327.521 237.677 326.286 cv
cp
f
250.834 295.836 mo
240.204 295.836 235.375 295.256 234.982 295.218 cv
235.506 295.536 243.834 300.608 250.834 300.608 cv
250.834 295.836 li
cp
.599145 .271214 .303471 .00570687 cmyk
f
241.102 267.332 mo
242.3 269.503 244.958 271.843 250.834 271.843 cv
250.834 269.129 li
244.003 269.129 240.129 263.701 240.129 263.701 cv
240.129 263.701 240.073 265.441 241.102 267.332 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
225.184 287.305 mo
228.637 288.918 227.668 291.782 234.703 291.77 cv
241.738 291.758 249.03 290.682 249.03 290.682 cv
266.068 288.757 li
276.553 286.689 li
276.136 286.53 li
276.136 286.53 263.803 288.219 250.834 288.219 cv
237.865 288.219 223.603 286.337 223.603 286.337 cv
225.184 287.305 li
cp
.016907 .00112917 .00271611 0 cmyk
f
262.625 289.146 mo
259.687 289.144 255.795 289.123 251.87 289.054 cv
243.934 288.911 237.816 288.66 224.581 286.459 cv
224.557 286.456 224.531 286.453 224.508 286.45 cv
223.788 286.45 li
225.185 287.305 li
228.637 288.918 227.668 291.782 234.703 291.77 cv
241.738 291.758 249.03 290.682 249.03 290.682 cv
262.625 289.146 li
cp
.912901 .670741 .584283 .647578 cmyk
f
257.208 256.275 mo
258.921 256.06 259.82 263.17 257.434 263.166 cv
255.047 263.163 254.816 256.576 257.208 256.275 cv
cp
.016907 .00112917 .00271611 0 cmyk
f
264.587 290.531 mo
266.21 288.745 270.531 289.854 268.751 291.016 cv
266.971 292.177 263.268 291.983 264.587 290.531 cv
cp
f
255.542 276.673 mo
257.729 274.386 262.028 275.124 266.578 277.254 cv
271.128 279.384 280.034 282.675 279.647 283.643 cv
279.458 284.116 277.228 283.775 276.882 283.751 cv
274.831 283.606 272.786 283.35 270.749 283.073 cv
268.373 282.751 265.996 282.429 263.636 282.007 cv
261.722 281.665 259.603 281.412 257.801 280.659 cv
256.584 280.15 254.148 278.132 255.542 276.673 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
246.132 276.673 mo
243.945 274.386 239.646 275.124 235.096 277.254 cv
230.546 279.384 221.641 282.675 222.028 283.643 cv
222.217 284.116 224.447 283.775 224.792 283.751 cv
226.844 283.606 228.889 283.35 230.926 283.073 cv
233.302 282.751 235.678 282.429 238.039 282.007 cv
239.952 281.665 242.072 281.412 243.874 280.659 cv
245.09 280.15 247.527 278.132 246.132 276.673 cv
cp
f
237.677 290.682 mo
244.479 291.265 270.142 288.31 278.334 286.291 cv
278.334 286.291 271.211 289.34 260.572 289.903 cv
249.934 290.467 236.305 293.74 236.305 293.74 cv
237.677 290.682 li
cp
f
223.341 286.291 mo
223.341 286.291 225.938 288.675 229.365 291.223 cv
230.75 290.187 li
224.527 286.45 li
223.341 286.291 li
cp
f
243.642 299.438 mo
249.934 298.856 263.97 297.211 264.309 298.373 cv
264.648 299.534 255.936 303.213 251.095 303.213 cv
246.255 303.213 243.642 299.438 243.642 299.438 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
274.287 286.03 mo
275.085 285.604 278.18 285.769 278.761 286.246 cv
279.342 286.724 282.219 290.163 280.469 290.386 cv
279.742 290.479 274.287 286.03 274.287 286.03 cv
cp
.912901 .670741 .584283 .647578 cmyk
f
227.388 286.03 mo
226.59 285.604 223.494 285.769 222.914 286.246 cv
222.333 286.724 219.455 290.163 221.205 290.386 cv
221.933 290.479 227.388 286.03 227.388 286.03 cv
cp
f
237.785 291.9 mo
236.973 294.145 214.674 313.508 209.11 318.928 cv
209.11 318.952 li
207.702 320.313 206.842 321.148 206.842 321.148 cv
206.747 321.148 202.568 321.125 199.75 317.209 cv
196.885 313.269 198.652 311.287 198.652 311.287 cv
200.18 310.188 li
200.204 310.188 li
231.933 286.452 li
235.133 285.378 238.764 289.13 237.785 291.9 cv
cp
.924742 .658564 .530907 .479438 cmyk
f
209.11 318.928 mo
209.11 318.952 li
207.702 320.313 206.842 321.148 206.842 321.148 cv
206.747 321.148 202.568 321.125 199.75 317.209 cv
197.077 313.531 198.438 311.573 198.628 311.312 cv
198.652 311.287 li
200.18 310.188 li
200.204 310.188 li
203.285 309.521 208.868 315.645 209.11 318.928 cv
cp
.016907 .00112917 .00271611 0 cmyk
f
206.842 321.148 mo
206.747 321.148 202.568 321.125 199.75 317.209 cv
197.077 313.531 198.438 311.573 198.628 311.312 cv
198.652 311.287 li
199.655 310.571 202.902 312.242 204.598 314.653 cv
206.269 317.089 207.606 319.979 206.842 321.148 cv
cp
0 .740505 .350195 0 cmyk
f
237.677 290.682 mo
209.11 314.604 li
209.11 314.604 209.869 315.771 209.728 317.12 cv
237.677 292.306 li
238.544 290.733 li
237.677 290.682 li
cp
.912901 .670741 .584283 .647578 cmyk
f
203.612 309.292 mo
231.638 288.219 li
206.315 310.59 li
203.612 309.292 li
cp
.816892 .52462 .462013 .225162 cmyk
f
346.87 382.303 mo
329.12 340.725 li
329.12 340.725 337.251 368.27 340.35 372.963 cv
343.447 377.655 346.87 382.303 346.87 382.303 cv
cp
.599145 .271214 .303471 .00570687 cmyk
f
140.157 327.727 mo
140.157 327.727 153.431 305.845 155.216 303.706 cv
157.001 301.567 152.354 310.406 149.644 316.441 cv
146.934 322.477 140.157 327.727 140.157 327.727 cv
cp
f
182.213 299.861 mo
177.405 295.942 173.474 290.612 172.508 284.483 cv
171.398 277.499 174.185 270.57 175.695 263.664 cv
177.205 256.746 176.994 248.44 171.52 243.955 cv
164.98 238.603 153.854 241.146 148.358 234.728 cv
144.527 230.242 145.393 223.413 147.536 217.917 cv
149.69 212.409 152.921 207.146 153.243 201.25 cv
153.643 193.944 149.301 186.982 143.483 182.541 cv
137.654 178.099 130.514 175.789 123.419 173.946 cv
117.989 172.536 112.304 171.27 107.874 167.817 cv
106.692 166.897 105.676 165.813 104.791 164.63 cv
106.133 167.882 108.22 170.81 110.727 173.268 cv
113.433 175.922 116.574 178.133 119.986 179.785 cv
123.829 181.646 127.982 182.79 131.861 184.574 cv
135.739 186.358 139.472 188.945 141.395 192.758 cv
143.729 197.387 142.979 203.039 140.976 207.82 cv
141.283 204.044 139.121 200.406 136.215 197.976 cv
129.82 192.628 119.666 189.85 111.64 188.345 cv
108.037 187.669 104.296 187.251 101.056 185.536 cv
97.8159 183.821 95.1416 180.381 95.6387 176.749 cv
93.9043 183.756 95.3569 191.285 98.5273 197.771 cv
100.597 202.006 103.369 205.854 106.444 209.413 cv
109.395 212.828 112.01 214.468 115.842 216.618 cv
122.612 220.417 130.982 223.065 134.767 230.441 cv
136.488 233.806 136.965 237.792 139.253 240.801 cv
141.618 243.921 145.371 245.332 149.091 246.819 cv
151.633 247.585 153.987 248.729 156.03 250.384 cv
154.009 248.818 151.556 247.808 149.091 246.819 cv
144.416 245.387 139.164 245.187 134.223 245.243 cv
125.928 245.332 116.935 245.576 110.228 240.69 cv
104.087 236.215 101.611 228.798 98.7466 221.547 cv
96.3706 217.55 92.7617 214.297 88.3647 212.887 cv
82.5019 210.999 75.3735 212.887 71.8755 217.95 cv
69.5107 221.37 68.9663 225.722 69.0332 229.875 cv
69.0996 234.184 69.7881 238.569 71.8647 242.345 cv
74.8184 247.719 80.3144 251.327 86.1328 253.271 cv
97.4365 257.046 111.627 257.468 123.452 257.102 cv
126.572 257.001 129.704 256.769 132.824 256.69 cv
139.275 256.523 146.881 256.446 152.588 259.889 cv
160.771 264.808 157.429 276.255 157.585 284.139 cv
157.685 289.546 158.518 295.12 161.715 299.606 cv
167.789 308.111 179.237 312.742 189.397 313.363 cv
192.995 313.586 197.047 312.72 198.113 309.011 cv
192.562 306.401 186.965 303.736 182.213 299.861 cv
cp
.0139162 .0128481 .0145876 0 cmyk
f
140.429 263.067 mo
140.832 263.123 141.234 263.188 141.637 263.262 cv
143.8 263.661 145.994 264.395 147.563 265.937 cv
149.863 268.196 150.239 271.484 150.445 274.536 cv
150.681 278.043 150.613 281.766 152.414 284.785 cv
151.922 283.961 150.786 283.319 150.214 282.446 cv
149.542 281.421 149.064 280.283 148.633 279.141 cv
147.77 276.852 147.183 274.428 145.97 272.285 cv
142.946 266.942 136.141 266.588 130.686 267.625 cv
126.039 268.509 121.466 270.047 116.885 271.177 cv
111.971 272.389 106.872 273.048 101.826 273.391 cv
99.4282 273.553 96.854 273.585 94.8301 272.289 cv
93.1533 271.215 92.0776 269.261 92.0679 267.27 cv
92.0459 262.889 94.6768 262.46 98.0312 263.075 cv
103.861 264.146 109.689 264.006 115.586 263.817 cv
119.863 263.682 124.138 263.469 128.407 263.179 cv
132.393 262.907 136.449 262.418 140.429 263.067 cv
cp
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 16.0
%%AI8_CreatorVersion: 24.0.1
%%For: (Ayib Makmun B9) ()
%%Title: (Mysterious gangster mafia character - up-01.eps)
%%CreationDate: 2/27/2020 7:52 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb",KBj9RffC8\5^+TEfmOOBa`Ec?r>?fU#Bse%2V+%$ZFAL5<kI84h<;'-[.R4MRF3d!EW)RLlFnRkoC3k3<hQE+o[HHrR5KbcF
%(/N%W9]k8aQasC$9i(4ZcIHN/V4Dq3rMJ.LB3N!E2Oh7'[B;BRA%CJh/U!jN_]'nFUK4Gg?a9!u;L4%8[LVB=I&3k*le7iVMXa#b
%,Gi'B\D=;ebFdL,79*QPM%WV,`CJAAAJ:MbQLUB-V]QH+TW"Nl=ti5*PRJsaEc^:]$kt);;Dp,rn;p7Tq5ct*3%0E7/B:D(3];2+
%Y!,pU/PTqZlF$M7ZX^,oGZ2!$->i0QiBhs.<0`bA^m!`fKH,nC*<+"S_lO91-5GQ-#2c75d%6'D7&,JG:%`-frO!X"=-^(k)*ls)
%>(t,6!!?PZB(`pYX7LL7eLkX&(5t(=b-F(X:?lDu$=DF[jJrUg(AW`pru*aI'^]%?_91B1D1i'dbN]73k#hG]iW9KW-`7E]G#?!u
%K8oIBT?'X;!)JcFOAZ]5itJ^8j1.i1Q;0e;):G-&a0N?8!db@<.'ZWcdd/NEn<lIm`BMg%c^!4ibXhfKCs'PG4fqVjd3($Z1AW*@
%E['Q=2f&D.$m1#IC8j'(5_+Hs'*gjDM3A!a]j!#Ml>O0f:&MO2\5EDSX4Eo&\+U@'%[q&iN6.05a%pjQTMi[^a8Yg)J!YFCX1%!K
%SlTbg%Vef4Z2`'cQ0UjrG,:L.FUcp5*'5a$LEH,m3J-V&$V`'9)t;U^P>E`gq2bg,W_-"5@cR]?WtugYI_m$-7MXCeK/Zn0D-aKT
%\YD=h"l#[7V7X.-C:2npm;fl]djSY4)_`j!/lK_Gid_Ar="IXn_J>/dP+UldR>-TDUX`hcjU/)@cY*FH'RhL5d`ZX,fOX5"Vc9^^
%B2**Q<(aE4XkeA]?.A1?X"VVBCN@^Ej;!+]gaN[1jf),[<EMAIL><>VuG74OUs%U-k.`9CDJ,0mBhW*d3`<(bqfT4>l$C<',X,U
%elT5Mi8lZc;CE-ud/%^rZMH;5hdJ0!Yc;H?gpEO%J(j960@\14`r5e:=KbboDt\,j-"i+dWg'MuiHMW)3Z+Le#a3%R$0(JO)8UCj
%bJMdNkY*SX"d7GXC`.\-T,`=^+]"\R.OC>4MY9-meOjZGT=8PE?H*#;>,BkhW;rG7W48[<MRQ@F"nXQEXcXMg/9q4>_g7/h>/-1;
%^rna0j/2!L8^Kmu:gS[\CX;F[5F$!<W;N\oDS62)Xsn:8AJgG)I;(rFgH'T7W]"1J8"P!XPql;JRhf$Ce=7^6ej&L/p!6H.kVrBl
%ILg<_FeSjslnISt4V9#,X9/*`\iq%^S0KbN(U`i3dUJiB=au9F#4IPfEX>KQL.tNZWd.>;d8k%Z#jC^PCf"flC266lChk>^H,BEu
%XRig1)T_Pcog5\\2?<@0R\W!U&L%dnl_?a?<FDD.Xo3EHC+2BB9[W[\+E<ZlP+(*@=!Oq#ZT]VWns]Y%1]t=\iY3d*"X?/kL*!uI
%msh/`[1Li1.<8O9:^Dkh%-+/K%96>1FcCf]p(r@M?D66,9'W#9`JCaG;-%Vn"@g$S:#^)"Vlr<A(nq6jO!D315/gPl`p)E@;e]d4
%Nf;GLEV`FtaBrSMZMVXrgGXK.'JMPITB_^(a>t>^l]b[cNi^]lFo%,!C1NlqXOE;S)Es0)Yth-VCn5IE5LjN-74Hi<IA@>%\FAIY
%e4<LV%QcN\C&MU$RkD=/oB8tBY'7&%2d>Aaok/uss$e_MS6l_?QGmT#6l$9IG/'r^S'?gggCBe?qpt5h0sVdM[?_'=>9h??K)@,0
%:]NtZVdX"for"^bg'T4"iF`n'A>[8g,hVd"N-T>C'eNLj2-W[)_I@Y:3ABQRM`fhOIiA!Kg<Fa,0*a@?p?8Vj>C07?C8%G&,Ii`h
%?O^b9<Rcj[`jnLCY^X9=q@-]Pf^D?eeuOkM73<n2oQ&Xq+gCuA<kL@$%5p!>Nu:"YZ307H!?1rXPW9201P:aq:m'QOK2cs<lBPQ=
%30LUWl-G?d?qAp[KC]V[!nNo<A@*6G/iI46MQW(4ITPZ9=5-F;NIe<<E3CJ_$=VbA=_[4YMSKI(#KR,&[=[!A+.$b4dSS.9A3!pO
%ieN<P%?D'$ZqBZG9l3=T(DoVMr1?DI%*6W>+pVe?A9'Mp4H(V:(Cp'\$h-/R[:^653m=$oA<Qj0(?pkijrFlk6\^6`+#t;l=0G]7
%2-'UAI95A*a+DG"$T99edjT37_\!7L=/IO._Vb9"P@-VF5Z>gjG(dgnlORadb8!.0nh74#5A'O3qi#hT/)U0HLsTd?X7G2Vo=ELt
%_6ZD[L#G,#Tgh.aW-RCCj+RG(o&;a'fn^:O_uTXQn_-jg98A0^YjY9r*IEU0.Q9Kf@JHV)UTD_%:_FR1nJe%V&Mhh_q&SoVU4-4j
%mjLZ0a+d=*Yto5X,i$F"OQ;p[9-a2*e7ra+TN91j,S>2Z3kcRJ2K#egG'#n%c7]D[gQ9cLVf7HZK/D6AUF869BEH1;TXn9:8&1f3
%C[=t=19n8dBf;t`8]C#":6`aPb;LtbrY+UBjB[hjMb+s7BU<?0@DL>8i@8aQ1rm>45>4OPOkh$<F:9tb_8X=00_L>@)3FE;1Wq0S
%1aJS8_Dr"AHoqtPIq7c1#5cpL&CYlB<6BpY*_lsCqAb2cNTu."C3Br+9[Ocr-Sc:bWHp`Ie<FP<[4Fg-'hbb.rd!pq<e$#=BLuqL
%CoQ(;c$BM(ZTbl:4[fb(I]?AJOeQ<cK:A<-rMBF(V_&I#/ObRE(%i(A28NJ0`kHIcWRQB4LBg8Y(o62]5u"XgWMp5O/lkQCQLHeS
%p.<tF$u1Zg8j"]l93ElKn-84b$Yb#D:a8Q2<SjGmi6n59aN$;S17f@P0"lT6[.J>_QQf+?aS\'9+1W(sD2*_,rh=1J8"iSHhRR&(
%?&EUpl*//[Ps0*P;M;"YR>`*(/ra>,BX<+OYtOHGc#CWS7H#[_'>*l;(!b7JNEqDq[363!+$"M$s6F-\U>Y9tE0UqR]2\X:YtG\+
%2Im6fK>]B2=eHqs'RrX>;X*5]/k)/]O>2_ZY_=uoTib/-3',M+j8sgeK0l?O%ZZk<5@:!HHAKhmXj/"/bW?Z_7m+S0lelc<*&dI,
%d@]u\_il<)bsplR]si(%rj/s/#KiXM3k&kB(;!t4W=`>kk/Rsg&R-6[`=Z>PQu$bDK9DA&p_P?:.^&E".?9eTLafVa`K@1qnQ!O?
%;]IO2&Q<4LMU!6Na,%P:+.,!m6_[A'ikr:L3hj!a.4m]W1h2"$O;LjKLsj018`jJ;H_@AKK(CCqWfBjZV>UER3NN(Q<mRegn&L7;
%Z&Gbp+S-N@NI&L`4tF%HqGaGJ?E!2tDel:DCF@7b5E8MDKc;--3%@B5oO6%>iZkPcn/OooWGhI6j&/@%*;M:Td32#0IZhq[*co7^
%c-[\)e:hucE\nX$)<4d$0#l*(Q]&\p/+<uI?%Efsrus[CE9:(87m"CCD=&S*1&IR^nq2el%^k)I30>K7j@6*o6acMA/.);alu'm'
%m\5irOKe^^rN/q8<a+d&4+uA]LH,j-O6"Nq%Pnc,pDL]"KMFO/#jVso04phR"A\_Q5Xri_`-r0KX`VfY)N#Dd/>m8Qr8;2:SN]7h
%U0,emXd:uS'g^._7ZlJkoGU3Hr1=r@qiPF\0Q%/=\)>.>))fWb*UW"dJRGF#o-9h/3nhjQXQ4%RJ$b\0g<C`,k)X]WrJ\>8DZ0mn
%bo^4ooHADLM0b*Wq#\#<:":9Oi")$lD`E,5UE09[%Xs3W3SM=L0=Rs'CaqibFhm[tT^_H[>-6A*0i,/[H%'HnKm3o"$"fg1pUbuZ
%60pL+jlrc#<-.^NW6)FQS>G8`Ja"`P;PL9VJHco&J)+H@#9t6%!H6oCLR!<ckP(=uj?E,a%_GdsML>=]Y/LosC]2>^3-$U2>`0iY
%idYHhXZUC!>$D#E<a>_`T^l>pq,s[ue^fVTQ<3@?;@<k<)#5-VY.sKEG/(9PDrn+W$G#ZWKrO2>K0n'%qrH6Vr/0WXFF5iOpXN#m
%)",GCb.WF@+W(_EQAuF)Ei9:t*JKpk[0-P3Msc,[r,Qb6ETHBPrmLl'V46`'?Ofh7LR@<JacSRRXpqLWc(p_NPQ^[9)"kr`2=a.p
%4Ad>!eAr:\njJI";1FEMb4%LWSg_K.73#2M:Xm`/g*9BmT0,u';r._l48u_af*[6mp^nK^+$7gIK-*,&NF-/UF8>=ImI0uZO<HW/
%-Gk^2#L<fND.T?eO;p@r3RVn\K^iDikfeO"J^],XNRjLq$/9>;alIXhP=LDENF_UlT$`Y@jR2B9O^*?:E]LGd)E*^?3Wq(09HM25
%Ms-'^3WQ>'_7YDOI)H-F[LO>d01reSCNRmj]uHCghoTORq:lGQ1]Kr-DS0oDj.dSk_1)?&RJh#@;Kksi(L]cVgt4KZItiWJL4pAn
%T:VKd$ctn;bL*iuOKsgD1?6.%<p=&7rs#7[]e.ic[W+$D.lB(8LX3Wo-#T.hM18OJ;;rIQl@T_G[cRF<c4l?21AcAd:uhhZa$lFp
%"DrL9b4^Aa)h[)p@K]HsU.1#<[H`gC2aJA"@"QBgcC5>RM1$*$:$0>RD^*D=jLc?jDar$A@9E*8_'SU-Q>>Qo)*)V!Nq3H<*%`I/
%7,#9IDi>kB,(luh7t[<9'-!EK56e-d1tB79rb/@?;?V.!o@]dKqP5R:Q;[Z\Y=(ond6D97WR][P%dGIL.]lU)gXtU6_!2`$C6\<!
%>TXDiWY&"bX0ph[@GQE&RA9AC5u"W&mReS0Zh#D8D00U.s.U%a@:^$=aG2e(lU.9-'h*%cdct`_'LboHE:[PRR]KMMTGl;b2lG'f
%CSTA1r0cX0oX,3SqV'kt@U0s:p88m>(N$;,Y(EP@!?A)6"-J<na"rUP1L.*OY&YUS*p3e]NfP/4BX?jAOFnU/ae^%e`J,UF0Z&K4
%iIZ13kT,<a7c>VB=h#T@i;T&@Nu"KK4PKaYn%0\.0l"V;q16'Pd0$sLf'17:*O'Z)M9JH_l,Z'e)spRIrGZ;>^>fNZm*h)F3fNb]
%qe77lD4Zd)o0/1cb)WeQ"*J.INXXF"JN>P_7g<_CeOoW^)lR-/p%#Gd7%jR(Dk4oD%qhM(RaT8DICBu,'NXdk=f<G>Fr>HrAp!*+
%\2EmW-T>QPOa`O9A4D=S1X9ZUNm,*q<+OYV@%H6VRGm7\I6AWa0FmS%Quqjr]is$RMcHVG\e<JMl4'RhC&M@uZ5:l(rN8WZN#U:P
%KplQ#+-uDjc#1Vd/rqDAb[f>SYu2f*n[i`R8#3AODXuXdA<WN)SbpKenh7Vi<QD%)8Vs0r\3:fF0)g0REXYGQSTdmRcG5(&;tYSu
%W7eJ[W,W>mY$PnPPd"@B>t1mh_a2P44d*#%et/+h)A.DR?]FFE5#*Tm-1o)%!2Ij.00'i*(YNM'q7#qGNj=LWI5%Tg:U-O$VH2YW
%[&jMP-\Ij1&TRt<m-@G]F?:3]c]05epFZ?cnIQa^ChOT6LRr5:KH=DNl25R03Z'q#0F,;g:SV5p]:#i#j[d3/.@h][1WV(TON_F.
%IG<fna83M37QjYrL0d.bl"C$UITA/Ta[]1S7T&hG]NrsF>T:fOGF+rn+a+RP-1#Eg6"/%R_RbB:^^)`R^^jcWa:9H=-mV-u2'Zgc
%(,u,L6E7V/`"-`TOWDM1,E*A>dX$1;UYNO=N'd>Ma3ds=XYZddk-#]FjEDc2,B1[HOi\2I1m5uLImudLPIn0-7WS<1alUU,CF#'-
%[R_H/2uVt.gn@W]rX5;RFGoc]#;aTVd[`%?3/#f=F$03\r%mg&LA1VP^YjE9f#,=Y?[qtja.VrWcM31gA<ei%-@(Q#ZY'.Z_Ui4Y
%O`(R`a*nHC0Zf8:ppq=a%laD[L8;PS0J);^jt\#](&^4S!rB+L9a=73+6E-b34k/,/B?ql5Ml2rs-<=q+Hnl-8?Ve3RE+e=37Gd6
%%F6ZW#7opTXrn5_8&9$]`6F4Ti2$#V&N/"eJdi)GnqdWt>XPUl3u=#BEHasqm\B"G+'T"U5H.i#U:hK^d!JuF+i_7Ncr,a$odZ^%
%f(IF`0S'Yl:]!*Vac,fX>3UF%\POIjj'j6,r"tg22q459O'(C36,`0b&0aO/1H[MS6;qXDV[-Ac]L1uJ2UQ'rnTbolT<;/Q@FFmm
%r-V9Ys2aR@Y[f#,EiWAl08oM[i6ruEO!8ms*6^J]89_Q.9lD'AguE:SCu)6ab%*&Lr)Bkj5qD#(X12$@XZfg$c8T85qpZbW1A\Vh
%C)d/DP)Hq[>[-<9;2oF.mH.(.7&_W]WYR\HatV>LQSJGU:8q8Ecaj0d<5#6^X%E*@0"k(f-g+tG@mD^SpMg,Ie9o8k1[l@B]Fc>8
%826^raM7M2TZ>%EJ,!1Fh3QVAi7/30T[BN<rE#!V6:.6T"*@@in_F9KI0(rCFBkcW26*ZVM2Rqa+#B>rs1)17j;e.E;HYV&8MPJ2
%A7SQU#>+!^0[3!HSIs<gGi6QJ+NsO&n8^!O*b6]=]ACD:bQ,KFhtZLKT"[SF0*"f7,^+CX`QD][nf.r>H7m)88_O5TYnN\7#U%/e
%_dR_c<7P#BeL,QA`7^r_Cg+WZVp\^39;\!:TF;mI*&#EJA#s`qkKlQ%V%HKa5S'c4r@^LerF5,CB-eJgk.ZrKl&=6<rK9scn_Oc9
%;a**`e?2h;Eq-E@B5EIc`@RX=iId5S1?:`a1E$*SO\MaL1f#16$G]"YP7l;i_rke:PjQ<E9Pb[IVX7,LkZDXsX1'$&_<MFF3@4/W
%?uSHp?tlrn0,[O/@[WTXE<HC%-SO5fa;jihF$0(]#f5qaGa[s@oRpN&=`+\Q#l_MgIugfi$fn:/-Cr-E;_T3^]po[4*'Qo-8X+2l
%fFFfDE29W:J->/61nAcr/FaStZRi#M$k?8QYVZ2>SInD>U1?)/)BFVhXFj&Z3+Flu_i-SYJct"*@2DfKVM'j:9.]'@-R9ieak3R<
%!anQ1njI7Sm:]Z'Y`]b*4tRlg,u.-FnU"$V8<U^(Ho=FU"(UN8%!@A)RSMO19/'Nm7HQ">;OU9_9G<5'4I6XO6RH,+?Qr9<FO[!@
%/_a!XE]P_I#[erR-4?(_."KKUY$EN2+<`"aOJ0$0fe!70U^?_`=[ArV50HM;@F9q=M.p[Fq9lR1Q)JPWG+3k//J=<0GtB2Q_@'UP
%+(YXAb$dp\F58VJ\l"n1lm;aL,+0)l9.@17JAPbfV`o0iZ5d=(Gp)EA,m_+SR&*(!>6#/-5=eqq$#IGJE7P#hra4CEfX.1#<4,SK
%NE,;jFXMY?U.-]l3+*_/nfSL2]D.SH#?dFG.!t`QN1<7s"f&9-D4B?c9Keh\49hDEP)#S`7Bb.9-jF7@]I`u0/(UcReGjS>@@gB3
%*fNDRe4]0f[FX&/=oalQa.ujgiYm<XX:?XtQm@pJr[L'1[ZpXgWgQ'DihpWh56b:hduSQ+[H?LF7;K'MLFM+W).,4K.U$Sr24CE>
%kKnj]=K19X]K?Rt!2Ke+ongJ;!F3q89CH>q[<g)9kcTbCl_RZ?oGVO$'!l=;OeR%b"<+)bO)/T;_0lD4b$\B.JM02BKi5P9Wk\Ku
%[dYn])3+;@s(%`Jer>@IIhY><RgPUp-prR5Y[csJ5rSr[oD>]1o<Cisq=O"OMtcs2-I$;%oPrNnb]0oK\Z/U2B)J]--HCms0=asO
%?*J31FC,!OU%(44:HTqY0@b!<.K+]C;dr&,(Y(K84FC=EeTW`,5el]44_SHHH4,t8A+cgDW`>Y4``)XP4$S$Mq?(/bqNflZ]j3/'
%%g<$qg:J3BS(^c_TNUqr0\N$&1KcWk,>:)[W(i./U]7FuSr[$7(=7lkNS(L6HYt@[C@&%YBK+07mS;8PV>V/k=.AktgXmY<ma':L
%b?"_8eXEMWc8;r.[UKa<QaceLej23;U,H@fk=W(WC6a:j?A#"7itIPrI<+%Z2;51:O#hLjBn\gFgo)K7T]l$N<%CZr%qmld-Les<
%3J)u`/pfg3;jsmZR:PqeW`M')/[c-u>O>JI)RBa/PUq`u[4^."U:#l$74ZF"2*<TWp8k5/!m-$>AGdqc0hq$P):iRO%9SI!jfMfg
%)up?5gMTc*<,5o==$tC)r#Zb^V$nr^c_kHOF6I_9F*C:`d*9Mq=9TIGBrTI\8fTh,[9m_BdVBk"k8CaY=GPiP1@JGnM`Nl#hUp7U
%je0$mF`MW,gjraACkP!ZT5*^ZrMLCb/QZ+JYu7^29Nf]>`/JC&C6D^pWVoeb"WQkFI;Dtb[<thi>WMS6Cqe]*er*/n>]$jK9cFVu
%hk$SsG(/h-N?ZUBen##0.F+CI6$(5"<^EjfV3Zg;PT#;:g;,LSNfVk.e60I_CRqNVMu't:9*t3[42]Dfka]:$XQJPjPj`k#&ph!/
%DDFS#I'H?m%s/62j=r`]AtuC(5#F/#1Si!K&52%9M`i,0%-R#XWd,9\MfT`k&ib770Tg/Z_#De\3)([Se!+mLN-Gm.&A*1$^D15`
%[BfW>r/L*7RA?AdG`ud2W>ZhpMN.!(@6`a\ml;7^-C`rrl>G@$JW(_Ek.-0H[PXZ-4)HAlRi$ORk,\0dAu6Ca;T>nX:9@Q<T]fLJ
%h7$Ccl(BcIJ#N6ei9Bpg^/L41I&;DP:o#muPMc>:AP;Je'@a-+!b(IH/RT.:WN%!*1n=p4=8042;Q>PcY1h/>([*`mY"^\0V$*.C
%*"'dQej7#UXdZ?U$75hlKXVY%S;KI4RXn@P<FR(n'cLTC*#4$5="?-cenf'n=%b^=6>Eu[T6>0j??e#O9\5>dWL(kPO;r)m:gJ$T
%TA']nin1O4;lrs.iAOR!Sc$hpM0,iGDDZm[2e++(,'Nf]eJ_W1>18#p-Z%Fh!0T!)PIc%9Ui+_SX6)Z8+e;7&THO&<cRiH$.se-t
%%][]=f;,.1Yl3a>FPr^Yj:A]"MY[^H:;=uPA:BUP6%WVm$Kq)2?YCmS0X+TBiA9d\6%<!gaKV!dPP4%IKU"RL->4PoA]OY#en%u+
%SXI(?Vc0DTR-uP*#fN^JNf"9Y<McREP]FW(&1>R)=-[Ad=-dgYD"u%&#=VJ(.Or>#<AN;ZV,h<E!9,sCOL>m,(PgCo%Nk]WNIcNa
%$C(_Br-2>C>%rM]Ljo2M5d,V@*"-8'%9#'aRp<l%$rFS\ekS9=9&c\D)Td3VC4\um*iZpQ5.,_:'K;:!@]o4RfH(M)Q0V))-F)$'
%$ri?k%b(rC%?\pA5ZrImI%>E!OTK-<il!1b_X#:pd0LE"=cG,0RoS>VgB+=UMshXc+JGf_7;H(jP[T"YO\\Cg.b%m3@YD/LJm,2+
%9P0FG;N*bCi9KMcPKkkVMID,/KI\q`3g!tW^Fk(/VNrZ1&PISQ\(pEsHVRg0%UOsBq+k*?I\tW;L=7Boc(TW0N2d<7*I^FN`/_%V
%IIXSuZU4<0oZpWcF#k(OFdkD1.Q,*$N!t[U`=$="lK)POhFD>AOAG!I5'cSUPAWMr*@e!QDP7,G)u`U(Xj9T5\0S8"DS>Gseo!DI
%eiksu`s\7-kT6L%Vr5unI@OjY0,aRFa>4UgKKdJM2HWg:C&=B`hs86LZ-mqs:uo-QgVOP8rPnH`CSHX]DuJoLTt@a-"jsgY)]6H:
%NqtkL3:>q`9bf(:B\UdWs#H1%RMair2"Hf1:*@PWPt'aZXL/7/Ff"e2%i?e)f.<2%9eU_`<bd0cY`Nt0dt,ln3NPfQdUJinc`l)e
%[(=AM0`'CFS]^42@^gDOOCc(N8BFf,6Oe2J\>gi+L/B54hb60UJi\;6Z4E$9L+=W^"<c<($nZSh2FG<]4Za7p^QJp'[,?]@FU^@:
%&k6+PYS\%gl:t]@Im)E7=[7%g`UmX?A5/;cc3MR)fhn@A(Ws#Bn(<X)U6Un5j2(r_X4HMBo6+,7S!E.T)^Jb&ORO?>h19u@:rT06
%c/k!4fJE&=LJa?!aH<]K-([_6ct)5I=GiRKqR3KV:k(.)o@9.$hr.8@$>LK+D9\&O<:gBtd<MX+s+^(foXtqR/J.nHV/NehL10XV
%(`g+6AA]mMo/'\%7&*W!_,MGBc12TYR]8A3U'VC4=k4VM`WqHf)eJ`:ke,UJ`,>sOV+.k'RJ`R.^E\L]=WB*%i_.(OSnbAq\Jf=e
%*OtE-q[.iT+<>>]!0K<AV2Q\p&ZUbmb#u<_oIs=G.KagP'ap`DW26\[fi0;cK/atb<n5333LpY_)FigH#mF;F"`:]+9s=$9[2NsY
%lCMd_mqp)TG&o)rRnCYR"Q`U2cdrWNam3dQfi_$"CJ>==C""H[9W(Oia[T7j,GeGA#D#Nd\Qp_>eT#YV[S:sY,QBTj6C0%pTLRE"
%7/oSDZr9lL.b>X$G$'`9OH_+M8jSn%e3KX.E#Z;UTXYFn^U=QANgO?P_jFL#R:bRa[-H:9T[F#%)6d1qDK(M1Z)0]8NkIL-@KCqQ
%+E0B7WD+*XB]PT44M=M?]lQI]3bV9rTZ+imGatI:Djf!aCbsId;9Go9#3noZ95Pio3=(YJ1?k%fW0X<[rif4:-`e?PiMi\//NdGq
%]'m&`H;V']DHEu![&:LYV\.Ehl0YroorM7M!9cZ#198G8gMIE2MW(5t\kq1"&Ze=jMt"HP%_!7lks$?j$<HnXb;8/<4jc>$E:JTA
%05.#dD3o"f%*8cY$>9;KX.oDnBn2IPHOZsP!L7@n[8-;lK<(^j@QT,Yb[,?_@#R2Q$Tp1!4Fk4Edu@dkp#8:)<EMAIL>=X#k
%_%(%-A_Za#<r#7fSuLG-C:+_`EcZF0eS:RU8q9Ao-uF#A00i#^78m\]Rk[:@M?-5`m)!B$f8Egj23jfdRSo&(HUX2CVDPP[5PX/Q
%-Z?s.-"<38dNna52tjl?N\peE@eEn5,W!.X?0f`u/!V$a4rkXq`Lo$Qdi.FCYuVm7.&jHYnWPH-Wq%+hOH`50=t/\:Ce[$,!JYep
%gEH9qh2L2p%G5/07TY)F]<=&$mFj(^KTHIo,uQ-bpCWiHBpD-n#;g4'BJ;\aXH%5D6;+;C!Hj!pnlXGS$1:_n,/Z9#IgjTed7G'#
%Z$&_R=64:[*?J,W,1sVQeNFF!8)9q`)<8Fe49OS,Yl0\M<Cq_YG-I*GecchJ5tW\.0=8lkZYn-9j:%:ZQjQ4(H!ijDe08ef#Dr9_
%40`lR_!LNc?E9E![/-9'"2pr^:snttHkB\G6&!q+43hqh"M*#b*U)1gCJJ//6QL.8NEr*cjHPB?!a&<&i/HhmfQ0$Pe/U*9^lNNE
%cKb0SGY%q:W-ntm:iGDV]OT;#9GQ9B@sCH_:7tcd#=ms[<sZeI.s?\T#'T,084RU7@)8O_Td'AQ$ar$/Wq40g]>Ti##Fl$^r)M?T
%SP1L=eNmdI?5df<%ePe4RtJgOqsYfV>.#3'c4hlN_9(h@5._hAVY8!no*Ngggftt'YEd5L_Z>M3<1YRj?9Ig`"3]*/CjJCjfY(d+
%-kWD"[R6RD38Y*FdYmQJ%H_h1.flF9NfsuaZA!H6QP+,0Eu\[kX7$\L5'A<#:4UZ(6fJ?_6\_cB3?FF)&.#Sd)ns7!iK^KcG=PiB
%Lp=(F<\Gr)A*(2"7HcEJ/1M:Z461@f7Z<P6IQP7PQ"P4/.5C',52QRe)Z8;)2$`?Mh($um:QjY202\NS`/d&ZY2eh5mDOf(,B8;m
%V,Ckj.[kD?J5_QK!_+/<E>Qq!p+Z0]=4I<B)=$G8Qa<d@:?,I7!f^^5#?%KLN@ur8U*k(?:`l1l1Oddb%HFPqC<6k&hc!@#W[D3Q
%<WG<f5->FWNLks&L/P@JK&)*<J;mQG[LslV%)(n<32jO1nF.N\$VMR\8'l'HnOdtC'g8gQKQ3Coc,V7\/,R-Agu/V)">0oG;Gr61
%LA=aI[9p2I/*)p>)GG+O]?Ye5%W*i/>qlAl<QH1#AE4aG7nqj#O,qU&jOV7k@f0hd?//#CmUoVlb-\Uc?$JDu4C6hnN/t0";u^_f
%T5M*up@@qA^H74qh78$oJ)fS\hfd#[(M5&7a6*p[TK1HK^\7]#q=2EumIL>h&!K`c=kH@(5?HfPl5]dsV"_;VeNJ=+dhSr,DdM9"
%R"pHb3V%7cg\MG.RT[V%a_g"5W8Z'4noOCH-,8EbYN4m7\'i)9riD9$\&HS6#0q4Nr.JrG05aG+=L(4bo!t[q[=;,aWQ*,!nUd7O
%YO4$P`U/V/\<SL$SenjP4tf3m<ISq'CB*VFe&>6t=&ES-[[*p0*17,$UQCqcf5f79f$SV_1R1#2'?';)bi-=ns$7Cp97VrY]6lBL
%\+"^E(W-21S+FrDdd4pIrTr!uj#=is;ac)(%F0?QM,$7J_@(7u/=cL,JN(%u-p2?KKc?6FqsuasKJNFSlSeQ8OD0J\T).C6Ni)`K
%VS#c?@B3T)jBerbkhU4[V<;YIUi64Y0(eS#0Cc8OGi:3JU\j&^r%5UlEE,\Qai0J=il4Va`$[iSrOa[b00WT9jMd"dERELsH-5Qg
%mSBFX.6C>\c+<RK=Bmp`]o:9qqeP6^jQnn0dGk#glM1:Tg[+^`9*O1#HV05M[[.B^9./=Qj)OKTi0%k7F/4;24?*:CC'r3!(u<K<
%ZuPqZ*AK5M=S[-P)ZQ,,Vn\$mfm_-35H9NUR^Tq2gfeH?3nuj?2V0b^X'ZM8qQ4kIX*q\CG&Hn1(\tJ@[1^6YQ*,,K;tY0NrT7GE
%%aWFAh#95m.=<)l5NS5D?`onDOd=;_BcB85\toLGkB/r"5P<hgKI5X6N\J(/Z/_gH82`8N_T([9"8prTEoi^Y*>>6h9hei+?>3cI
%R3O=5'm(Q`2i`&;HdgU+k3'98pl_i>?qtQ0C\PAnc%L>3BeQXXNR-7=,J?_Bo0U^*f*;H4rSpPemb$(Ug,JiJ6A)',qN/9OoBUOK
%Z<BrYg:\]W*$)5Ch_cdbDU.IZIU8LL?d@IJdX#2(.7pR(]ui"Tc,.tbF@)V&FE>^g\V1]3Y7p_PC+=Wqs,#;WQbW=$A+tjrj6S`_
%3IOr\g@FS&)n.bOHV*[K0>9Gn:eY-CHH!;fqHZZ1*M:U%HBqT:BiW?P=2<&>Y'Xl$[!Ud$'D=KE\ZUcNC$d1T#e`iQPYth@H<AGh
%p*s->-X(u4.b_eaoG+K0kQX5rQVt6EPXmk/p=e<5(]A[9Qg/cRFaC6l'6DFOoSc7c-(*i[=DNEHVs!5$md=f&0!;@IqopuU3VfFu
%S#5jNR#Q3/a5r@_c_s+#aM2;aV,'1FBt\Q_HKEM_f9qh$FcZ/%h/l=l\l$#Oc]bj1V>^%?Rsn(<'qj0#_p`CBG43h3n4e;UmM3;V
%*QpZR$g7A.GJClS?+o*tF2nMg&URgIR-Xb;O0l:-+jkVtNo-F-dOSasY)Z)WR?QH^U0k)7ghHGg5k"AZp@]nsW,%u2YoD,2iC$Ag
%VFu4uhNNsFT3gt3mc'lnn'c'a<B/jI91(Yfl!AA3MJo[7lDN^$?`s^ie\gabVcW4Sn+\k(HSmb]BnP.*Qh]*iDm*NVk#Ni-HED.q
%95>p]kgTKPTRIt9/^.(=]e<-Nq"FQq>b+%MoLc^B48uh-4ZmOPoNk<0-Q3B.mA[dJ'<gSHH_%Zl=@h^DRPJ/FR5HRGp56O9FqE!!
%Ydl`U=Y2qCb<NZ+WQj%tN*]kt$`$j&(EaauGYGkdUfYnCk4W(GSj-FaY.&SZf;26*]gtnj<_gr6f?L1:Y.L;E*0R5VEGK(3_\]5]
%WCNM(K_s?kTpno,k&tr%o@RiQP:97fc`2'N)sB0:-G?-UZh`Ta1B'`2Fb;o9H[;6m35!TUKt),JI-g3"B[t+V>cWOkEZK57'#-al
%hlh6)(SKg+EA*D2D0OAAZtF<K\)TgKi5u#$=?;8mD`bqliqcillMERqiYg#8/[r>q?8CCRC(t9$]X`N)<k.GQpW)G5Nra1Bhb*ld
%:WBH]2@@e\85fip\#8WFn,@55=4s\R[M*=ZiFR5fs7;0?qPipfXgKFA;JtrX`hBRgdm)%`?bI/o>s7B)rboPFSP9:#9mrRR!0'lb
%s(07NmB(cFkl:AUFmdb.9**baS94/uq8;fVlMD@XrVASBXU-gj\r&$;T<BQ40)a[FB-k\"mAG*3*X">!oZ-N[n(Zrl7fCa/mJ+n7
%:,^unC7RU/T/p,)2(KBSN,W%[G2D"m)]fq-3R50HW6UcBe9YVR[/mr*Uh2;)ja>g-qKBAW)%ZU[(H\gJDEE.=AS4n(/F71((+qB4
%nX29tm@A)@>I1m<p"+TE[dTl?a]XC$F-t(5ai[e,*nG@@(E!(i\Vh&7'>j+i=^9S#cjk*?Fa[/XKuOY\#bOOIkf0Hs,u6;(=g40p
%gF-)6aduOI]`Ngnk'_S&G*#M'WCc:X9qcpG@ooim?[[8)Xg]JMpPO-U)kl(.UFGhOp:==Jp:<:Kn*F%(9K=8D-4>i?b;1r<G->S(
%TQspf/@p.m?dL^0dX.8An,7U(a=@S+T'#I.fEOOQK#n;,_)IumD]X=R`S!rO9r36895F^&?9qis776V2p"\uc:^:8krcE8Dh^^/p
%DM4Iq[nG:NgsDc2erh%%i)<Y"E=>Q^h\MNeK'Si+P:0itlTgf#UjpnYkaEdtZ<]HUftH^A0Y>.A+CIU@Ir'OI=8/W/J+Z%:pd37]
%l[_E[;kL>]gnlQOX$"/,?#P2?#6@U^k[<$h?$gf1oRD,j5A-j9MkIakp7NqW7LtLkS]s"8.YUTpV[=7`G:66_gOIcnF6*csRjP8I
%4A\irL:[1JYN><XP!O"6?dtW\r%i$fBS#<gK/q`SnB08I^'-m?i>TR!YO>4*J!NjpZsgMbP]$bdBp)i7jHoA^SS8ZQrY]3^V[gL\
%bj?GT"5`gJ>jp=R[qCFDDfCm!eB61D4\>JYnP@dUc;)a;OJ%,>)3Ib13,K_Z$rO3f["bG6Kslr1qH^dl!\]%^^tG[2f.UR71#[MN
%hn<srd6LK(+<#^2,T%rN"*u?AgsCQ#qEmR(;_DK%F;OH;E1u*klO.S_Ztj_+M[NW0Lq"Wf9:?6CeqdTc,UdVtR*h2,:@RYFd='sJ
%gcM%Hp+3HEP9gTae@c!U4<Ja#qtB^qSW?^k;g7.s-5W;I9s0[B6RK:Ir:qUGE]PescOa9c8)dsJ2bOU<fRi7nG;Ng&!!VP0^[B$'
%;fW0Gjsd"Ck<sNP*aRtnK7D0@H..W:&%gU15B.>W2BKZdK%od8\7PF.S:Ne<-V8CQ\d+&Jg@fCWcpnO!RY#GT>OrE!19n=P-G[8q
%%Ig,V7"GmN/.1cP!_cWAQ^>c8K--89'c1bT#Rs.LS-WKPBmiu%Ie+$Pa`m5AHYl<-9hoP2-9K4J'^)H?X%]#h^=,cie/-_Qe]\'$
%J7[,N8Ct06KkY0P/nF1m<(7#`bBs0(p@g[T8[,Qm?WGN+em:/7?qJ>AaJ:R"s.N.#;qMOJY6n7/p:OYB'4)&A6#>tk;2:uCpdre+
%CH,!t6:K4-HCg^4X<9ouO>RGIoYN0]8,_'+FI-e,g%<_OFVX?a7U51mODa(OhHD[Z@-H^.cl1M;T^hs_Ra>-/M*1pbOOOKG/Tk7+
%chgsm<nRp?>',E-e!u-ZlS=`^cu`Q#T%;U4B@af]jS?IW2V<@#cdp*87!"g`m*[V*qmnZ@$\31U1S3S#k\7j5i`,%0P0o@NpRf]?
%#Yd+7`+8u_Pp:B)P`^VqY\,4\XV1p!d5nlXM1]?`S\bs8;42+b;.S6AnTcqK$dh!EN*iB60H!T&bjB(/lutJ1Gjh-J/QRfe1`NG"
%"9A)m^t/dg)rN':G[Q?6\SqK:Q2<D=fmc]0XMFN;0X:aV+@7>2N>;5&2YX>aRJtE,gpjpT1"N3L^]W(n_M,?5H$qOJ"0sf:jU>Fg
%<b.BKe*N.]#bDPKdiCuPAU>UQ_kVR'8IQG%KJX(LR#%;n&O>UK]sS=KYj!<SE@T^VLRW9,^*qg*noj4Dp+DjFimdPSPj`Q&BG#'@
%6a%`$mP*jDjAH/tgS%idmr!0oS38kkH2@>'BI!%>l9Ijq4W=pco7q1lg"Fo?Z9La\E_H/m"ZhJMaZ_G3PK6"?!8'CcceU@d97hRR
%7'cmo"h8bi9YohRE3%[6:,TY'7"FFLZpT30;7"6$bJhRW\d:m/`_jO[<iB8F4!?PK5L2*pHYHZ(kN%s+5W=ZI+r+8.h+(#R@&A48
%Gm0o=OY$iUTQN+<Y71&5"hNI6a"7Wk2=B=Zl\6,S?r@Ja=o`BUe_Ho0aleT/P:UH$mWZ3o69a^HJ^ijZ#K$fY=n6$gB]8T+@e,'a
%bQq@j+#^O^A>1"I0.jW@f'8en?Pg</ipa#hfd;S!#(7H]mPh^]d;;[fL)<LEeqt2"Uc6fp4pPeu;\7Y#.hQ<cd98/Ba!t,4>p-qV
%N]Y7%*_d?l'#>:CEGL]iq"Vt826K1'LLAh[FWjF8H?T^ETin>ATLi?,Vf!Eoh6-%6g^VCSgs*jG#jMTALj(L+R(LbT^2k<G)VoHn
%B^3*%K)5>J#DK:NNVBS@?!m<Bh&KZ"(9\8(%9r)XDH':;D(%/`ZP=la6bRP"CrPIQ[]e/jFn=;C#bpVimR7MhZh<]UgT+*$>oHJ$
%q)6"?WU=,#0l5sW7We%R1jUmbXfqq1eIXX\\hen3H1tVCiW[2e0"*+4EZ?U:QU]W>?bcGA7Z:;pAZ?aKBZTQAlYOOJ2W5*6N2iQI
%Ce-oeQ?VP3kV5fWc/Kjoh:l*i^Xo2-50YT"^]*G]O&-VFp@^3JOf'LP2%"E?:p/P]7NoBk$kL^(MR>/6W([o;,pS2X\c1"fm2oAh
%+rn3+CY2IFp<P;@TV3F6IqL:/E"%c8@q`rcrVHmkheaPb4aph$O28]\ZW$X95#.6$f;tZ@^;A9k5Wri_o$J?:`Ln;XUM"N&]clV[
%RhMGQAbu3\EmSUL?e;ZA*4fROF(jrpJMR1\guX!8emA:qn55SJ[>32D5`90'5&*dVYT3p<F\s;Z,Q`AM&VC%)gCo;VTZr3ATjBHR
%CjO$ahG(9.+"o@;SQ+LCW=F6Kj$K*/fF2^<FqU5%Jau6tRsp+c6>PCos%is/_&j]ladH%C1ARWgeKiuG`&cn0-CH'+8S^M9#6lSq
%6(V9s>0o2i4CdqN$U)c^Pg4@X2TPt:j#Bal4C!/Yl)2.koZh=QWW:jk-<o6F]@t0V@k[(o_7=q7Z.==a?Z7WiZ!#k/3aZhYh\aK0
%lpEg@E%^%84*@h*FM?[<53Sk.l$U=XHt+1rMd<4O"m>G"lg.8*?Z7XT=D-o.GNjLk\U<hLKHAS;Jog`M)j83>([6>+qjtL!o6CD1
%4%6XoH,$Sked+3pSI2!=*nX27cSHo/=:6.83Mp&#9%0Sofr#d<@0u741X5n%hU3C^iLo`rJ9SXEJ:%)C[iH-\$Gg^u?;UIbY[`Q[
%>PND3/\uDYnc9+UcV@n%=;;V5V9b5H@jNO[O*-23#AU0Sd1l]OSh\Uo#X'+Wb^^4DF9U,fPl*[/Q(!T;SY<aDpjk@s-`Wg.@FrD0
%4U<&SoFcm>cFUi)E</:J1JI4k+e'okEmROV&/2gc]9#8Z1Z1#Fm4QW3bi0bb[(Dnm>;o37@#KOU2ZC<'_,^mXTm-DZGo1?=GC\:s
%WSG'A$RHm,\hc'u]Z2e)3E+Rh@koC^UfReVp<.e7$9s,a-RM?Q6fe;m"[j95383.,EK\60Dfb-oN7K'k"6Oanjkig_>/YeFME9n3
%_IK9"L$mD)]&^2.D-jOlq)=,GEst'LmW_?BjF)\\7i^!Rn!NJ'o;nWmjLs12)0C<qa;Uf)F"B!iT:&O!Hbh^&L=R69[GZZUI%$eL
%6XsoJ#;nme(lF^[!A-!8-Sj$MG#GmORYSLDS#455gfj`O7hE;\GIF[sU8aB!qJ*,oP/U-*m7[.:D1q$<GoE@CXgL'ckFOZf34n=p
%T+G>BIJmZuZb>)#4h)SM%pE0L6&sQFmp;s-9>I_38m"EqEr2`3Kk0t?<AJb*rNN+9OMgKeFQesDR<B!D+<2S!7UFG"OXFQj>i,9n
%BKY`.H%o5p!i(KAfe`SU5h&GNBQ(,0l(;`*VL1?73[=kY,Z2n4J3#M[92F<0g\$p'dIagCf!Lc9S9e?ag-8u<IF%nP0;r'4Zb:n3
%c<rF1r;2!L6/EP*A;#51dC_EhR+r0\@p"l+2Z[FR3!#l]:bd66r6-RupI:[q^*_[mS&7eRRka5*D//Ll7'@)\[mR(Pd7&YKEuk%N
%)+H7a6\<\71-aOTj;bVkgL8Fb)pF)4H75kF9D3bCDS.o<Sg7I_L[3K;6P9RYhA_p<NX7UaV!K34JFNr?3=JXoMNf,LB!Zm9ntsEE
%>Y^'-l*)SaT]8c?+[bgV2)`;hdH,#k1lclGp<4<#bFdsDc\M+M$IkW.N/5(CH(6<#U(^1*J]bLlbd#2S.=N9<\oa2^1rV+_^c]Sp
%6J*t<Y_:#?+EZ%J#gF2:5=aCg/MN\VaL:!GR[H)MEpd>-1@pI\-uX-4^GGJm^pIu7o;KL0QrDVobe0O!@G*I.9H?0eTmgP7&YlQP
%=mEhkGokQ$f35AGH)@1QS0u`jAtEAjI@i=e-jG$3fO,M`H"5fis%N;V\W5.+]?XXH+nX3`F^c+k^HsS,D.C1*/oXg'<UKr6Lk-j(
%G,\tr>!_8YfAee\6c"%Up@`6G6TlCi@+dKk&EALQkkV5(cd,%=A"KI2U5)k.1cjk5b1sp+%81*(a!adtk+4C5mB?@"$;Hs5h=QUH
%Y@sWVO]?OWH'cOZ93"L(^=55-@OiL/m)m8s)iBfFkpP%Q6bOP_6!@^dfruq)H>@sK\@r2["?<c$.H_^]VV,sFRQ]dn;;!m"p?Re=
%,g#Tj+\08N-ctom?INqrLEp#O6=MfOa+BIbPmN^!lR,egpsQ>kfe<:rP5Wh4,hXi!8Ws$=46YC4'Ja4I\JNM3d'YA,JeGiJR7cJ4
%8'4-LQk=KZ/CBhPHi':sB?XuF8$)A6.;u;Ge#p;Rar@\TpS>7KYG=;:f,8JfpR%18[lsV[PXfj\oKOeNOIg?d0g$nG9k1P+8@DM.
%kP@Y0r>tXnOON[STiU.*C0_ke7pR'IX\\\.?lC%,aOhTZ-C790m7&i"AIKXd3QT;9$tRCr'P9]:Lr!c1!ls@e$VujQP]0]nN:@rP
%.TRLH?sJ[B1nVhK[Ejoo932Y9j7+_k@6B"+h8Oa!F80i5^@_!p!rm(s0:!6+j%DJs3W+MRNb52j-(XRbfQ__l-?OCWbV*`%e!cJL
%?!1(RKWpuC-buiZbQM4FQ\]u4Lg:=+MlX_dB:iL/PEV>mlCD;9h/,uo?ZRFk4Xf"Pku5F-f,=SWZ/jMPH-DiGk=l<</i\1MUtnbZ
%W&BG'k+SpWR!Q+$g6(_Lc/8<i$:%qh:\Ni>=mhJLYPLD9kAS4i?NOJ?Z<pQ$mr`R5gQgU\=Q_i$l<O2)n".Ba:p7pG^:b`H1b[J$
%IA]0@Gose?/ZkI!q^51Z@a+"QbAY,fF=ps%I;$CG6CUYCrcm)/4q4VA^DPU9T0\Em:LCRn>,o+<1tR"6A%q>[>8/['GHBg]34Ep$
%fk%bL$+oAn;hR5\2QfH>Ze<!4Phk5J7TAFs&RGCX7;hKP4JJkufB^fuA*7`/T<?dMVqeE+[G,SS4VBOrc3A3E>OXMm.'\CJGN&5^
%Aj,$bD-L"Qc<jf_]hbMHL=CH]]YH/pFp7Ro-,"ZR>m$ZA@[!3t<^L_eTgV^]>B6T%fZ1oomh!lRh7M"1,I*;A-!5mlrt>3Br\9a(
%XZqYj1fq("!7S^$5$CoW7jMX6M1)b44r<&I-a>=V]=XEJ5"\bd,N,k7M1)b4^5tn,DA)h[q`KVF]:Xq.2!`j?4o:AokCAo<;lRC=
%]d<C"ZMJr)<k;)nZYaX4q.]e&m%^\e__9B?`'.@*?ftHXc\[(WX?N3.oJNn7[Y?j!oJNm,9S)J-@NV^$s39Q+'DlH;NJ69**&Y")
%Im=BA(*`j^]>QltT_1=MO-\<T`gl;4SCg?lObO+@T8A!'X?NhLmR(Gj5fQD\'DlGlBMK5M:PjL.a6E:HZ&c=c[W:-3`'.5VZcC"u
%TT/k5-MGj.h'&T3')UIb,Y)#a98@[\D@6-_@_ut,*^i,3b28/0`bXTHG`Qe:eZLj!2b`>siYFbI)AK,jFE`Y8T?7alM]XIJApq_\
%gl5bD<OfJ'`<4GHLQ2TmbT;b*0j.2d7Y(j/koLKG<BCk(Jqb#;'nhOf@Q#nkB#5#se$8P1Q.\.mVqY`cCO&W$9^N2CpLJ?7olg/'
%$+/Xfs.\`CeB@kS5\'LE8%ecGa33GPs0'Tc9Uh>pLkh"2q-Qr\[!hs]&#iJ'jaXs*+djt_)uC^UFA&g(s4PeC`^ePUs*B\J1"=#a
%YCiMVN*-)Gmk]ljiur;FVAU"e^$7S*DsbV)i],*Ak(BBM,38C,eai.&]4'[KW7MO+')FY4$G3sZO!Q<#/0mCHa>^U`;NR>b&Ra$s
%K_rd+;OWij=cYR+VV`FR+$=O$abREa51CB#,l[/4T:o<4>/*D+cJDYjXb(UfPXh%/1W(muVrs$4.!0KU.`qTTcOFmT\u.75ak4s<
%Qc!gS+*.2])c8BXI07NoW<=1+ABab<mhCH,(]?JQ&6`SfFrC0!6c."M,@bd%1H./HCK$f:R'$d:(n6(*'\6.1Y&9nW3I$aLbuqQ#
%jZ.k#bJY6ZG9O:&%Hh`?`cMG=j5l<G=9"@bk0'7,a#\F:L`&M!#RDNcm)M:EBr)ol(m'IrVGL)V7S#o=X2__ATifrNl+$O5/;k^*
%*]l_H0I\^-#d\FdkqVP]^G30EcX-)=)tR\#0i:d.HiLZXeYCstkHCm)=9%qc7TX3QG<ZuQ95,;S]g/g2Pbp24!^V<^m/>d*3jW8-
%opbp:5>O6LA%&QrER&NZ7jq11d_BVU01>VUdnTSi^0cJ/mkNDJZ+p;%j_-6p4,EdKnNLL\67XnU=4E$Y]qNMRcVC[ThGE[o^<fLA
%flkcKn*q/Zs%R>TAB+t+@EbQoe2lh+b/2tBL*&H-QYL^2cM*?FQ:*3`=BhA,4<U4=Om5g0=IZHhcQfQG>PFF9&8'ibC/"VDIp])m
%BW;!`T!4YbnL1iD%hlR8PXoA/'0*)MmgX6);7".5ohF52GO%E4\LWYZbd]pBIsJL^[%po]i=Rs9G5M+n3'c*#j(#6OJ)7:d'Vqd.
%[1'6"[anp7fGHH7)d:ZAD)[@>!^YD\0S>FR^jGqSEZ(a>,hnBm>?dT;=Pi'b4Yk"'0JQdHI,Sam-OGEm@TXISH7WE^03i=inEJ0<
%1H]uJ\`EF!g(6YfoiL>?9OF$T^,:YEMlYlZrYOR*cB=\2V!cG+AuFo'VrVo)9fH.D3e)8im-Yk(Y<ge04iDbqjmq0*;d-BONILn7
%9jDu99*otOk]LfKp@!-B:V1U-]"BJCP<Vt4YC8KP!L\5$c^X*X?EG2-"<'kC>cD'+V$+po$_(K=)LhL4r`P$WiFgFff"?iqp\TF(
%O/^7cfAorZY?-G/lPN4k-U?g[C""rf>+Z<QI=!"73gmSOKCBN<eWr/NkoS&nQ540k:AQ>6AkHaKbYSh]BkC*f<L+sZ:=mK'l=oY+
%f4g#@`B`VsNRT4_pj?f$,><aVX4)-Iq"%hOi)+%2B&5X38f\Dn?ESEd7#XV+cTcFk%d.GpdJQaP1SHbiR!WDa2N`K<<M[D^?R;)D
%cEiq#=B8J;'7fA,)W4'q\-rMjD!6Qfm-/N;!k/7%ck]!tj!%m]b#7^;RA5Kjg!1s'P[T;!_ZP[ZL^n\+Qs=&FA"H'NU&gkO)r8lX
%^apgU8WUd^n?&0+El+Z.FeO/n&&.:tV2kJC/j-VTOaLSI_7*f8m.P'*Hq[?3SR=-f6REUop^X>hm:N?FM]9n4VdZ_Jb<Bq07>#]C
%[$,X<V'L5,\unPL!c7^UrZjC5`IQ?`2QR^_>8MC)Sp-Oq`+[W/^6t@J13).<QR/3kc\[e\*W`/<q$6%NEu<tP#QHm[#[#l0mQJ2$
%cA7/E*pmJDmiDLFSmBA97Urlj/0)LO?4U]C$#O1CkGS!7!p-K4fc-*W4dCP&ak"J*T9Nh_e_S,`Z2]ou`5&1e.>IEtQubo9+c0I$
%=.LeLe0e7P!@,UsalRl-m[Kkr8sluuY"fJ_6YeWs\"6(m>$*[[1n]QhJ4>s(_;4qj1t@psCqB=Yo[`pt8"Z#6<f:%WS$#[&=n<5X
%aYPb"OYn6.g;r_8,r),9]%`H;X2C$H6n9Os]9G_7Hpl2nkoh@82'6i)Im'VRZ@Z$ldTrarZK<BT=OPSp&d,L3s"1nAPXK"@Sb$=#
%XDlC1mD5F*XO)UH+j"5"?WQAK4T<uEKlje0h_-LeH.j@RXH3F!DPuU)?i,:TTtf?bYEp!P1KO@$=eRYBrXW=939E5&4_RpuHF5r,
%n5HH8^@3iO[_pssm>MD`K+g#[fM8g]^[NqoWE-];G\BmU+/`<<??d=R6L)pk]=m\fppNPY1Sq,[%ti\R5!/,Y00BZWm>qXJR0"T8
%D``(/'R&LA[Os]/3MEMr0p,s86<TqDU&8diZpGIR^$m_5YHrul&)@W>H6l#@$P0iTL@+-?6X"LU6aU.K$P,T:\j$uP#"q-a:[@Pf
%6^IAS5$W>GVNK8E47(TEkk;m2/hjDs$P-snOaY?mG@,S3gr`ao4b^RUqJ4ncfT-CNK@BDY9s5Tk4:I7F)l"LE'E,ec?sAX(h;-n@
%LNLs&D``&E"?g%8(^s-ZL1G3=*X7CE]6uTR.2ROO*_&b_lW;5_pU3G=XY\sjLHTVXp'VB5ZSiWEDL-1Kpq.2cS#F@8%jTk.fR(bp
%g2Yl@GGh[c$P.l.4#FH4mh[#Lq\4"1TJ%*&qVf)9gS$]>UjIF#$JrClo<^,&4H'u?d/AZRcB.G=gC?C&FFW7X=!hhW$P32?6"4@9
%CMXe.#.Vhrng7K/B=RFbiZUSC??d<'G2(6<`sWE[1brflMp'%MojuT)4:G8>oXN11>^.*5gL9R-60#i"531_/>^4>SMqe,_LLr(/
%eDP!P?'G^-fi%M-?M,p'4:G!8fA582\F@j8QlELTqXV"l(ENe<G3I`&n*.^,@-cZ^D5(B^(jt(H0>:1?cklj;HbAj0Q?X.++kc7:
%LX\%]2EKCed*79"SJn9p[R4DO>1<oj.?!M"$H/RfNscJNPB=Ka>1XQ3dZWU+YB-o]1;\a#$n8si\F;1sB>PO*g!bY1(84WE47nNu
%N-aGk&6_&!We$MsFrmlXK<f.iKQE]9XY:A8lh#FT3Jp?%0$#7W&FXQ43o,(GGu%7WcKXor2I1G[!*&']OCatW)aKe"!M`k7B6>/n
%*eP36nFe[.nX9+=QgHHd:f_6H<?B\\VQC0Zl^.?)F5t`WZnstYbiQk]l@mRE_K+UGEM^7Z]steROZ\6=UR,,$pJXH?o%Dhn/q8Ac
%cg<<lcai*.p)S@)G?a'%(7GQ4YVI_MKu]0imdb_0b[&KE?F[%GrWdYt31T!BC_V*'qpX.Am_4srjrP&m2=B59q)%h_o`;Dg2!n4h
%<blU/VmL&ak!3]8\*f]KNU<WsGarIeWM\#7Zh;i7prVXk[#gkXB%=60X4rR*WLB-scNqtZVqH!BjikZ^@4Ap8O[#=@s1GA/(9OB#
%k[o!FrONqD+4#=iAQd5e!a^7ZX5#)#:FdgU`#,.$!,'%3I&38P?`Zkl>o.#m2p(`MMlWoKAJAo:GS,bIS7f?M-Zbo4kIS0$P2KP^
%A"CeMI*$GVPP$`6RJB`-\ZL%<X>GF3\<`);gH$_S%*@>]mn'YW[D%6(P9+RIV\Yb\f%1hh%X0JW5qlQNa'#m"6Nb?FID1]-LL0<@
%M_:QJ\K7B$.he<EH\d/PnBTFfnT]*:!?nq4Q]Za9Rms_D3h,li=<[3LCGS>DD!S3PgtXseo&X;"B5+rJTA)Ri!&O<8C]8>QmKfcg
%7Rf7G$CM@UjkhEF\nSQ)Ir42kUqrDZrE8HC>Y22lDcb>1KNAQK0BhZA+N9]:kE>?kQUp&\K_8nsEVJaE+,BGfgph*8k:0)ekE>/Z
%>^fa6_%@d6gZ'RE@()nJXrNL<phnJ6LSa%MP4^$eI.<;!XVZ/9pE4aQ\',=XGoJHK!]=9FP'^sPFtpanj&_39HatHC$Y:fsB"g5q
%@'8VnJFDeJ_J:4Fl1[*K39SCZET;'ZI\(E9I;;k&O$lN*ELssn!Q[gf-HLhp0S=V6!AZ^dTZUs]cc966iL'-+P-hhD:1`<dnFKCK
%SePL"q"A0:EIDkM;P24p,4ptAj,u@AEN@]0AiJTId])!r:/Y5c=amNu?KP=5V=1j[0KAmbok11ha`KDn>7lDA0ZmS%5>@=7K\?$T
%*OEHT6g,lg3Le^FDpG@!3sT=AgD8poc'p,&+kWhKbZ>l6G>#,(Z;<uPXJm_s9e1!1>4FG0+BeiVVJG\m121Xe_iJSn0-24Z_p]X0
%bPbuA6!2GS#d@W(75UXZ&o#6chq)qtA=P&[nFJ\TI/A61R84@+'*X8dU)gEV,C1r"NY`5dihK8?hbJVo)mag<9+f8jDJ-o3MY]%,
%P^?!`:UdBmac(kG+Ba^Bl#HQalp&(2_Xo6qajtUOlEt0\9C%/hp\;unGZ&eXW@Z]pJ7Y7g$sVU8;cS%%88__p(Zs=NdT>sqPPhnR
%kq%tRilkmgboQ4(b-54H.>'%P\OhQ3iRT%MRV+Y8D'p;V486`4RHXRHgH"2\ZbSu`bN'LorUH`[N=-(,ST=#B1HH3"f59neNc7N3
%Suk">HR:g)1lNp,%h'2d!Rp=-6?p`ugO!b)#`>\sA'`]olCuRtpZM5Il_iDXR(9O6Ir=NXCj]9Nh&O0WX,m1"M-0MFK!-4@/(^nV
%m]`W@/M@_S"?^Pqd)Q4l%///4#6jVQ-$5=T`j22-[oW1Y+GKKr77Wb$E<&=G4\&=q%X#hDmHR(kDmNPo2FjG:V6BT6hd3e7::@mG
%4HBbgK$0Jbr6"Li+SRMO19bfFVss($>upHAcOp<H;aUMp5IHp73F&\-d)meMn?dHg$fAQ1aC;rW`EKc\kYNQe4>2_q>eGpf"LY+M
%+(S3+cWbW-ph/1j'<&/Ac+$-SROMg.*1A9))fNtYXTm-[mq>'lfLnd4>Ambl]!_]eAUVCo>SCCa26QfVLe]GDAT't@jj6e"R,4Fg
%0cMOcG]n'u)r!qS)gqX2O1.gGD1_ih\hE'NkfM!DjY"r@&`)"E\sm845KpDPNL75R[7,(MFnH]i>Cq)XoSn^n7tQ9>Vr'AGnoi;)
%o8hr,i`as=3mY^jfbp1jl=o="D:d_`$IM)s"eQo),qnED2X[Rtm9cI:f0N*&^)CWV#]8GLp-;D94lMR%l@bi@hRLlDRC@`j4^LqP
%l=W.kgGa<Z<7ZUK?\)a`4af:bgPBcM5^4D:(c.6Q^cU."38?RR!$cO$iAK*2QaZj0;fi"JH?K.T#m;\k"+cugU9dVdEZK0c;_T1L
%Q="D1%Z$6b/2a0\*PGVmT'KX3!=n2a%RL?dE>oe8V<6SB"#:F@-;f6q`OG#k/3*"R-RQQ7RW(g9I?=Ofep.pjb<-6'FA*'W"1\&V
%fg[n;M0^r1P7AR&fH?LCQ5CMo9jDu`\O=*>M-MY\/b87G78Um/?q'H;EX7cuoHBuhh"2eu%7Y"LB==1P2.k_qn",?@qT3VB]W:T@
%B,cgM(HMgL1nblW39!jA4DCQNZ+%K]BUt0iE2SOW*;0$6J'8l)o&"`)hR)ohkF:YkJ6js)O"T]0duL7mDs72)an:U,ZS-A_@iAF<
%O3WQP%3.#RfaTnqbmb(o)]]T;84S:Z"k/d/f93hK#etRGiMB`lBJ*Zq[W+=:(jurJZ,Z5q,aNa[&PtXN1mnF$0Vd3*A%>hA2C#eh
%cJ-f9p>^GD=#fIG0VK05:EB&?$j+&oh`)E=KXA1ZjK1?6V,lSreM2m>_fU]*d"QQQ0T+27MnKRo5r+@F>*7643o2+Ij@f$N7$u+1
%.$mQ*S1UPK,kf[+_1qRErop<OkNC#?2[Yden$X7:kPS:gg%dEVXZ1g-Q<0VA2`B9X5POd8cC^_i2dCGLbho"13\-Gt\"#[`&)5%D
%iK>r\8!IE1a(5)A9@D,*ioB],>M2*`h&g5V%1e<.)0bpn^4Hf!DX.*2$X6uI((F'Y,T-<'?\ic2cJaW8I99f5XOU7WB)$AK?fT#`
%IX-cp%<U4fIIeb&R(0a'.rJTekhZ<g:BjV)8I$K'k/gD$[*:Z0>[B8"#/i"oj4/Y92c\j?S@-Ts'VWk"NTD+MZ[JTRH#@NQoSkIV
%3.(^d3NR/Q:=+?cpM)j`E4eI)'m)h>7%NPplB$5*"r%pH*.M.2Q<hcNc,uZES1gJ1ZLoD-N'cTkS.V/EKq&[qo+Q%e%lV`'k@\Gf
%^^j97qJ>pGJ23H#dq\-CXtM5#)UcXg,]V*PcD1-r1J]]aI.[5tZmubbX6sTUJ(4*V+BB]j?,W<%aGnFZlt/]A\7hFO<timf>A'e1
%j.2rtnUg.R+DqE/1*u+D1miHAVedS@XKj`UCX3!LX3&].CK?YREpIO6h7LGH4Dttg;jH1nnepl_?18WDHkYr&&"CT(h`Q^\-WY7J
%mtI<l[,n(_&go@IG!>)N@=Ce:q[?i)n_u^#fO&mPZhB)h51]6q[%8PVJ_cSMgl8nqgV[nM94[^6XhBTrc?aNhSFB6tW0kK5'qI"$
%?DgIUlZ_$Pc#$(^IC]0Q%$IMb/PPUcA`k-n=1G<17Ag6%mS+3Nd&1FS1%rPF0T(#K5%418cHMn)>jpXtbnkK5h!:>>r",Au:S&j`
%PO&la_tfi&G'0#'=j5+Qo=HAO):Q.n-?EpA3CUH8`<C.905fk:*-tt)rVtHhrg+BVHn:i6o!.8$];fVD>33:mMh0Tf1A8KDecF*(
%f#mZuVQ(Ht5HUK&WVr^F>-[0kEiY9`3690&C8kN?@sS3\r%7;Tp?5Uhpt/#"kI]"oG+Q!dYPXOHCWap9GI-^[c-J:#I\:Ht2/76C
%:ZJFoGN2=_^+_a[hd"$j2nD50!(<4ncguLoni$7730*0nC+4U]F7mm5<PU[/kf/*+oA]`fmKoEDG/3D<[)sKLfF%C0D[rKrm1#b.
%m/3>]!b9M3'o=9BSsms4GAd$;p!.*Y]plXk424O=b*=>4PHEBQc"+/nP0Zn;T'_!['H^@nO)+'.eZ8#>>PkDiiZbs6h>CfD?[PQ]
%o,S,._;niK21$:ik/]et<C0YX-=8&EMHa72M&HLJ-YCUNIZ?r*64fq33PD)$(HORtCUt,Z>14KgOdo(3?fmOf_/mo8f@cK\9AIiT
%?^TTX"r29ePO!H*W`<Fc4X_)I=p:q;4X_)I>(mHnXq_=FXhk?Dl<BH9=p8Zj4X_)I=p4q1pPK-T<e1TK%uLOk<^C?H]jegX<B0ZA
%W/=#_FH!cT,])?hm[CSR^#//2V:nJd0$i#NC%ogEO"4V/$Tj8L=^!\NBO_-NS/d-cG'43Gei**PWUhm4D3Ok_5Gm:tD=c&7V,l["
%iL@Zu@)P*_.hd]VSm!tO>l?qc:*>o9?=jb-'t.R"'YlcO^(U?HCNrup5pD;`MuJL:qI#\Q"3]((H4-8%8i%3IoW#W:q/j/p7h)@!
%ZgB7H"n3Q'gE_L'/lGef>j&8Cj.Pkeco>:=)kLYnhDJU1M,,/)=NkcVi/#\(p5UL5_G:#H&N#]r79f.6Csp"E>`XQcD:6,X,UXCh
%OEl65$r36PIW8JaTCgkHd-VM@"9Q00G\<O$N<C+\Ea$[@#]`#Dls7mH>LioMP2@(X]f]&\OHZhA<-]Q+kb$*o<,Jm](NuS3B].h7
%;SqOS+O62DbK#<BCVTV,Z=,l[gb?GW%QtcNh!XIl/oAX3%jN<9Zs@P8cg9Dug^"\oA::`nJ8Ut^][IBlIqaBET+Y1-dOKmW=Xmfi
%`uGG#*H/L!`3%'XTPXZ]1@>&QG,F_[hKsE3S\)]s!P?2/bke9MIr9Hb\_E3c2jglc]\ie,SL,+H+>!,e?-rE&D>D#$6c+!%`Ad%:
%B/_fu<\WcmS7L9c92H-$G4]NSg2V$5!P8Yi6Xm=0/b.(D!<Us!l"W009[dU2/[-q2UQ(at#:Um,UjbJ!0aKqJ,BA`qI\dm.O9A]*
%D]?Z-NtZeQHT(%]TWt]AfiN*b!s<;!\^"&U5C<#G%p!A8WD-Q\!SX-jM(D=\g=XLFW4Uc^O,"6t$;E&+J,N'10\a*_ZeOF3:MreM
%^2CYgBRJiBIJ4a6JpqN(c^7b3oo<a$X1+1^^$9k2R!)Kq*Reok]eEo/TW$6V^tH5JE>p`sQYrM]^pM^:D_#l"kYJZUj^;etpnO0A
%kakS8$;gF2\Tsms^5Z2`Hro5o?@0WZRp)9]Xf%"h?5fLLLqs8m`mLCAD%O1?P70c]BR[AD(n\Kf.6-'1(7@UC/YkPi.&oMKJ.XPm
%(C+8l==5UQMQb9r=_FuT*WR!)Q\n#?PYTi(K@$sW*d@T79hr3Ic:j\cf_]a$U'YFJem^O`XE?Fh7]1)-4f??\na.8e%j?AD,qONa
%VL@ZR0uk_=*e80T$LSl,,srt<4iQ4'Mj0oES&qbDV9Z4'J.5G4bA7'DF'.Jjm0-tB3P,LMRoICF87%1KGHeo#d#r3ah%82o++'n*
%H7p%n,SGHN_9S0&b:Sma'4\B0llb`#UtZ93SU#CKd$%C?f<0ZecH\F,/U4Qq%R7/iG/]H!>/Kc@:*o$3\+IoG=j9BJT$>MoJsKEp
%PNUjpEq!rSfcGa7o-CG$ioTC4a\0os13L.9SD1WF=o"A18KTgu0",M%oLtS<]7gK2,93Hk//R9!BKJ>IMd/ANE[7aMiRl+jh5cV2
%gs#([b/BR:^B.4b&>p1NR]q2jm\B+8L?tRq-Y%R_ca'.4U5*6n?7,.M%j"b(1J;%VgO4'SDUYX130I1JD_F&B)tVmlA9ftBCGnV$
%j"c\[rK4C:nCSWWO-'l.AtO0h]]qo-em4G`*F\#5$i8??f>C&UaYEA;ZUi5pYL,.reEu">Fm!JMM>N$sWuKZRSqs[)hoif;53j/\
%i_3p0LA4$Xlr,,n/%Rsma=<#.;N1$>*&CN\U::o4q\(Ok*Q=ilh4LYCCa-G)"8n![\6Z[tSFV3n+*VL3kCtB!:I@uFD7(=YMYsud
%5$)L>b&8$Qb/a8o8,h'=VR&!`aM\3aL*&1:*Z)lr2n-Jc45)b\^&55n+$7L0[)_6nf%R*f>*XD.d=0-%8_NdYn`@S^CcUYS?mR]9
%,NBj($[=a/G%;\7]T:_Ff%C#4f_$oq]1%0-n.Bm^Qf.X<Q:o9VJ`D#!9-/aCT>Ju&ZK[U<k4'T>M7c4t14gppXqbb<T+\O/)h/mF
%ds!5;(#ujW8aPl:Mj;?@jn[R/A<)_i;_Z`\m%$6ZP:'[d,4D)K7ZAd&."iB*Z/g)A?B*2YQ0%+K[FA)hcW,+53EN>L?@PtDWW==s
%Q984`9"hf:;mg\C>\Q-`QT:i7gd1>*;QZ2HA')nt"o#_=/ZhotpA+XGG6=H486q&q[C>Wi7\+Wn'`8jQ]Mu:SULJs*Hi3jXDk^"r
%]\]fViIsJ7aRF2!0(oA'*r?_!l+<jS_cGKN(OU/M1d;ddab*ctU[g^Qd]+_GcY]M[.CE3POdSIr3PYDt.co5SBWZ]jo))s?]qW8*
%Y5D6,0+-"_9[T*6V?mFX[rb_3^>2k*jf5>/;VkBFp>%:kGh_0:^t&f.O2lYc4)Zis!?!sSFnh#]YFf\SmcH,/N(i'-*Q6>cS"mIK
%s5tB7*S>L<ia`P;9L8ljR[&!rP`YZVMTapF>*A][bZs.+M0(YFM8c<u#Qd<9,$T8WV@k=QiaZ)Tb+rT"asI6d:6?_L"4AJ*IhV'?
%7Z?I$Dl"-R+1i5S^R37m1)UQpdsOLAGTeD+Bc(npJ0GlnP`df,)lP/MlPUQZ'r=;F"eU^#-BZFX=<s"(An+nE&YtlrE\&j51,P6;
%`^E+CG"=!NP`S<D^(oRW(I8Cb=XbN`)&sh9ApA,gbR-%$kY2cP"Gf`&i=f[('M/VD"rN/a/;d%ss"p#!;^g2#1m>p4;[MZWr([VK
%r%JR&,Af`/S5,p%LLLH+JIt1^3l)1X>>Z"HBi-'kE="p++\Z8\Bc4Z[EsUkYq)4F.8<ReJ2j;>j0ujWX+NXA[(o&WnM+Cos0*$"u
%J0QK\5`;?YJ>6leYaU`2kek:fAFVqs#U7)PRZfNFJl,tm4qS?bfJY,e,,,C\.P57P@F>dIRn#<oT`G2A"=DH-YXD%OH6oLbi)i;s
%&JJDQ`mo]aJL*k-!4rnpbKu"Q9:P$Nr"97jiaP.bJ06-O"G9$4&ra\,#+EOUJRAa!AU0Xn(@`iF'Z!$o?=1DTTLoP&-n`\0K$+/`
%`>"rca?W%2)).8@@;;MB=HQ4PTdV,ebgQ?sJ?N2bLFf>'F#3'J-qak<cJXL)/s-`E"G'tf-!A5?3#G:\PR.8d;XcE%]`;g<=[5k9
%Kgcc2%&"E'Z36@=F_-cd;Aki)["(;N1BPF#cJhe,dM9tLc)2/\EXKVm3!H+2l2d-*YTa.6b]6[/U_)sZ6-a?l5LrI./$TYs&`jGs
%=qKXE'M4+_=]30M?qULnFBV<&4a3RIZ%8cm(7%]BR)]S&6Nn+RF]ONddlP3T;F2;fUpr8FZ1oH<O\Yi%PVN_("USu&+p4iUAb\lL
%"j<\e`%5:FY`KJ$!tff-QqG2-+2mmd#_e-ao>m>q%!k'l&4lmb,^b\0P)qBu+f>VqF(BA`!!k0(a<J-9(dTO"33[Qm(?!'-F](]+
%"P'ak\/hpV;[;[3+NfR(g'YV8Y_YfR`'lrpXF_$&\AUZ8=:n`!!7u/kl&:S&%:BMC:;qZk-NGD0KIW5'g1GoQ`3]s>5*ODb"7$'H
%Fq.?@r'J"[]F=9Ci/K9,8-;u"!<<^X`M#j@,\iu4i7P7<33<naEG,pu!oOftqMla:R\Ni8$&U0`?NO&G8g#6191:HBCd(S/#FG^V
%0na%kYRjY3,_<3DP)(=)&gC<e&Q.iR=@F?.KH3ZUO=E/"Md-TKND(0QQ@_Fg8gP;TTWBPnLm!jlM7BEci17qPTJVp'MT+!PYO`e`
%"OQZEI$d*J<>8jE.*[DrTH_/m8s&H1=9esBO,HX6*%W&f#snjLUIaYVfKYU^!Mi6ucQe-POhc$!B8')l^a?aZQXP0%5QP&O0bi]O
%8>&5S)&]CcTp5W[d.t>->RQ)ei_Kd\d$01k*_1qjFFom7@mI6u_Zq5/&Rd1&ci]c&:_jTp%m]0oZsK2a"e!HrN.>^[Mi\<V#q/53
%$1o[7,!O6bJ-'n)3<FI4^e)m&NT39tJ`93gq&fb%Q(\d00<T#XO:Yea+LVst!):"g?3HM/E>^/#R72#c"(oBe2u5iBKd"^pV82s/
%5QlA[)ZWcf64,cQnZa*>J.?=Ddn;i?J\jP56qg3uCf/9r\6(1X&ZVY>7<2E_MEn'o^SOp%FFoAnKr%T_:FCoI;-=b?$9("-\jYA#
%Va!j8"e$s7LG"O9;ImY.YV[lQN`&5-8.-%BB,Pq):V^4#8u:"J*l3Jn-3OgNVh#/I*Wc0Aablf_>gX:H:<hkB=,Wbf4<;Sl0ia"o
%e?uFfm$.aULFC<](T:AI3K@'liF^QMM@T1ki<+HP,oYbK(m]jK#q0mD`DK-l&9i*l!=<`D;,Q>X#`q`7qAG;@\g<[5.ENtA-AL:c
%1aSQgN51N?'^Mug#\p&>!6P:8_H<7eAN]CM$BEm_^,6+r)erKrF3+;jADA'\lsNWi1F5X%N')fK,"`dc3s\Omm0?t\OiU4AdNH=[
%NDm'>:l>>p/5,+415O+mUa!#&):AqV%"K1-'LQ:0FH:CaU<o$Y8AJ+XP;5bZ1<;O#$[`.041tpD4&R9fl^C9'KLTo(3$)cRB)tDl
%8S><S/?EVFX_VQP!Ne7VVK;l*/Ja0!Ql)dq.24/.(5ll^0d'p+]9SJ#VblrY,*jF1(s$?OdCL%Pd/ZP'9EKUbA5M%^-cdM$^uiFt
%&SMA_Nc)DU:#&JaSYjJP:hkh^dQY$6@1jU9jN8!EIF&pF$UWJ''*=mD.7GtG4;5a<$rS9;%D'3N/%IA$/<,U9kcL%&YB7&9V-jY$
%di!aKacfU>X$1>3m<;G8C7-L6mK8%d=HZ-MiJc6=jT#hm<:4l5gE&tL^gM2e?s!Pi70J)F[J!=GV^S6W](4h-<[fLM<Sg[HJkkEY
%U1NDaA'l/87HJoll_^4Flqb<h=50S8`,t+O;p2'%iY86F4Wdd;i2O;<!tLDZn%;pUMD/jV-@k_N+:1oETkf5C'ZedcMuaDk6Cf;3
%&r1k1;t&"/iFN"pPNL_Ll*HSn880G+;Fn7&T3U9tnkQA(R7!nLMrH#RnYQ?K+WD+O!()h;(SFSj(6-nnP(?,hU5nL`!/IG`hrkfg
%3^1LoTGe5882r2a!/KtE/.rA[84eQ4dHT6JP?uTbg^.p][/cYu5iGTTAk(a(1rdVSK$$3<jG6LJiWVGu&$Q>D:uM:O"g:6YdlBIS
%P8mkdl%I.(^3NL8Z0.+,AorFe`7n&b^<tc\*iF%Vhq?=?SL2?&0RlYflNEI&Yr7T168n-0O>Yg`N_!:mDd#]Q!cm'+9D(b5o=Ynk
%`U9Q@aCGT4!7i\F4K/!'r%It:mjqp,oHDl>9f/_(+9g9B\I&pu@(qO:dtS#!WNQ;@9f\[i2PYrUdfa3eLpKKJ>0n95W,Q=(7fnbq
%ALMGps(-;M"K7/bac_O3`7S<!*H72@D43H_YW3;;%MR]^KRQnmKk*SH8)4l@/e/%!;(2mt_<=d"/u+I;(>5>se4t;e(3Eqe6kuNH
%[;r*J1gUtlVjk[V<D#<`F\&-"&_/&IE`AGs&WTpfO_Itakl$Za56\a$<iI4Z8@*$Zp_SIg;V&.'>8Ed4VDl2QifiYL>YuJGPi_18
%=2SA8+ohd@kkG:Wd%Grs=^\/=%nT8[)3@`eqfpNb`8H/W&uAGj(DDmd%Q0V$nWaKt6aBiO4&E!9#p@]I24u,f;&=op4,K5_$&GuO
%iXV4D\4@60>pQ/e+gFY4"N<EWD#9-9_a2a-2W71ddOj+2.DQXglc9?4>^nRaH/(KuD$9ti*r%Qc.n9"t-Nu=Ue'Jse;F-!J;;qmI
%U]Q<PTR`XIZqI*b;`6O%F\+*VG`sqL6CO$6NX48G-_dF((uU[>'*nWgPRUD+(cd*cqQ^Il>=!B>L_6GEXG_gl8=!81,*D@]$@$:.
%d(*9N<M_DO)S>4k.kUuG-UO,\ptB.5`8\6uO`/F]IMU;[5e@e^M<G<[$),Z'Y+Q>Y2+IOqf9\1LL9dQbZcLrYO6!-!WH$k\1uSF2
%$HRft:1[6B`.Bi65msVCVY*\M;Sj7V'EFR'g!ga58HM`/E#KT<2$Gf<OI[N=o/cM`Nt\lAh6DR!9RNNR.cI$C&=u:g%-nLsKC2m:
%!odH@TEj_+QU4-%$lHL1Qnico9I?lO;cZHTgCbQTak9G85&co"e`)RgJ9:!(=#qsd7R9us+?tG@68/K#7M*u//'5A,'=T77?R2?f
%.a./W,$fDD&m=3+Cfuu(H:ciC6:"'ERKsB,rP<05+d4.o9\dJ"c_*Z_+q(VGP]R/9O9,P0]$UIqmco*E\W,pe!<%[u&Kj<>h>D.S
%XsOWfj9?34lc/fI#ZkeE^lB+"ncnsbWH3s`AiFK.gH._!G/66rbpJ?KV`mifUWF<CKM4#HP#e(a6)]D^m6dd:ILGU/l"!P7!$b]%
%0BhH/[s8e"=U(stZ)<RYk?87g+N#bc5aJg],lCm/JHDQ*(m5+<9,OgUUh4qX2\,thPL=@&p8$Q;Cca<iN;)W$S.(<aI:b!K[EZ[9
%K)NsonIe`0h/@$VV@Au<dG/Q5RV/f!R:EKTqSq]cXV'3V,/>ksM<9MS;_a/&W4Ju@&].UHghPib4['nFgXjU/4tMtt'g+D]#R;aZ
%hXgSbU'D$c,1J_"Y(AiKSM]p<1V,Uf9o-k7<Xp@)BP'ZV;k/(r<ch6hR1MmZD1Q>A%=h,`rV3s\o`u`o'uq;0&kf+=.Stbkit0o-
%W2hIA%neD;-#on\rNNkPk=f_Qq%)%2SfpY,0$B`EnO\rt2+iO^&LeO@.9JS3+(IiXl(C]Ha?c(8n=GusWNfbal@r1"CI^X?I8#H-
%@&QX2i]&EH(_g3%#E_Xnd?4*tHq"676tm*km6eq9+@tg!k/RT1UJ`NOUG+GIL'Ysg(Z68GZcM*3C=5s,^mQMq'=:#F*<J0I2`[kX
%F9XuM_^ZL6G^Z)UL9]ndp-B\MW6n+_\N"1SqC!Ddf`Ht0K)5hhP7!XL">#5??7W=98Pa*K8L%`]3$b.p;J0\K8#VbG8Bm%VDs@[0
%2/B@WCRnp<Ff6jKK&i2<6+dMfE?[:#g\%7mMn5<\DSU(T%I7l#q&'YZR)-WT1B]F?MJ^1g@qcriqii&b?re2CFtqL.V6"L0-:XrF
%g."q;M\4rJN//pB*L92Y?&Xh&GZ=go>4U;)!9lG@*gue1]co'[2\1%&.&\WN7PSpTLJY$Zka.XLciH?NeI\s\U!Ri-h+<!4O\F._
%8Y$2o1,qGBESH\%4Vu[Xao\U>OjP0,Z\t69X[h#b(]pF<),9.)B&ZTM=Fl?ihfV-Y?[,*I'LLKg!+-!&><TG5`Y4%ClCR6s&FkWa
%>qdNl<a[VO-W[fEc"Jj9-#0a9mUE9@bIR9("7X0DIW([fSMITB5'KWs?I]PJ*4#"<K/*[2/uj]fQ!m<nh[:t2:oILTpU!IYU7W0?
%I'_GRO>Djk<n;dA'7`F%)_4&4eT9+gKDW`n<!8:*3himII?f:+>=kY7\iN[oHRU7A39d[s31$q$:l&@6)+&Ua5=qQ&?!/G/`IaFN
%6R5/n[SA/]TouIjgB:kqOCRFj+65c4COXa,VpjK>)ee]I;%#'Z%+qB[fKmhuQR$lAUUZI[8WYD+#iT)cgubnk$?",B^+1OmDuk6A
%"_\tG-DuRP%n?nVYk`FF*tGJeEP1+igZdtK\co!G)D?0pXnb&8bC^P[84?rp#9sEJ#VhX>)crn_j@<5[RX/"OTp?(3qfRIL(.D.Z
%9n[m?dR<Qo6)U=@X+[SB&tu\olsp6rdip3SB]T\`N1^:nRC[(bfoq"5rDC5!9Ut36Lm+VSg1b,Gel]rT7tmIG)[J6/2up:S@%U]a
%Lg,bPeekctU#BH6!9""#.6+ZtnIi@j/]65fmb*-9<n91LQJJ.7q[<Sp_M.$-l=Lo7mA<Kr14P%@S?1t5IeRC,%Jt&^7*BThp=2"<
%%gq$[]"sJWj-[JVUiS:m:V%`_]/CNkj2oo]6W<pUAd<)6M!d!ue75^YHM5\$g,Ylh5IjL&ekch<X].&ZXbL+VW4CNB<,f_4"D#Ft
%0dK@Xr=:S0.)^_CjT(rG#/p[5F^%2CWi'aX=Y5tB.liIs\MQ9DM%g#EHLmSS6+>*bnB"^uG;Q@oJu=lYo.VIM$e+LGI_PEfe(QpD
%&n-i]$lXAF@b8Xt/E3*r.qiL^g`=(B5V`_W9lJ^=C_E$,qAN/Mk_^Qnm%5Djp;+n7Dkt]bjelM1)dZrNh$lU$="Y\eR4q8:Hm*&9
%W^1eM0:%7ef3.FF?6qVN+Yo*/i5mW<+-TQ#bnHG;Ln]nF,ek45Bm.7+H-EK#IHZ!$DsVnZ%YK%AN8lcKm[LZM;rm"OLKN\@bX("U
%TZE<;4.0P/cha*n@#P]'R:l:KG\ugFm>d!m7HqA)f2:he2FUZo@5MWt#A:QR]hb#M+\Gg?k"O`]:dO.^=M!=F5Y[q%^;'I'oX>E2
%#0*%*:$j&t;(3`529hu^"ZsS8+Yj//f`5sABSPB5($%(pfn-(8pc5cD#@L9Ap`MSg_l#HLT6$OQMT4M0g7G(O7LXMO(15>.4?ZGK
%-UGC&I<c1&)Bm`n<dU?R%t$Ro&Q2iAk9D\*:i#:/\4:o?XVV"aWa&k[pX?UThgPCPO5GE\c%!D)kqCqDIHJ0qQToosnONEGl)u^h
%qQF9$fmujNJ,SeB^+<Xj17c/QTBmQo^R%*E\tB\*"4J.X_dF_-b=H1JD5Y:H@^R9@Za)54/U(k$XBSNtULB0P^3*n5?.BeDc4k5:
%XCs+C8qE`j:*40m_`MmO#k`OR9Iu@:5VtG`I3q]"3778XAeW-,^Pj8iEj$<PFE=l!/Va2Go(!rfl_"t41T7E*dE1qn\4V,4,)-lQ
%,K:'sEV\\.1</-CIDns=K$rr*<Y4UcJ7I-(JA#pQaZFf#"n-j6I=F9uIbr)"Q'OE84@oL#TG]gZp&c?6cJt2C`U5tl+inCFrPZJW
%Y10!:<+c\BR[#kZM]jg8<MBR-*eQZO2Fo#SL'"2],Jmq=3L9UR7!Rl3Q%2$c7!OKS5DjH(,.n#2JC6Iii?>7I+g>W*/f-%"*+lhs
%iN[SNA,1/<KfN*N3<Fb_6KS?_jEuXNpoL'l:.oYjfj7-CFrn:h+E6@^SNZ!G+WC_FNFQIgYcE$`k9KlJF*p%(:O9NPNF+RZ4:2@&
%ATe^aMS^j#iP:"'"R'86G[!/jBjgoAhH9Q_1#Tmidb=%_J=e+u,!-+0+iL7ZT+6NoArQOMXLM-As6JTK3HF"gZN[M;)PY\^SLhhh
%5@>kLb-3!2(6As[GD?`^f%kGbW,pH[nT*R%3tF!<%Lc;ka7-7s^f*,Qp\7WUn[N*l`1ad8VP?FM4BiOO7UfVX8m.+K]U\7^*C,p'
%2<Vd3QMHHuNrsZS-*P0mG4%7g;dbmc^tm$,H'Z3]ghhgeGigJ7ni!l`o)^-S_i_3SE8O.=^sEE)A7KdB3,HF_@rXO<;GEr,odY.!
%DlH+'3t/DS0_mJe[iM2P<GSd!H!B3-?IF-[Ogd]jbhR%Ugo9$<E_iT@)g-+gb(6;k%5(-U;]3)M&@:<[8G[?ER#XU;LHYoim(&)V
%#KJG$W7?`I*-I?D=5:Tp!jGe+s$n9!6ek;geo\nIP]6+qV;?H&]3ZPj(gqI-RA5`jk2:b5-%G,fJC.Pc.CO@RE9'EeoP&T`BEQZX
%4q0CA`]:Qs'sko"a!ib8JtY?OA'E>ked_aCLJ"PDTnlUL6eUdr&"XgD+D<3h8b:.ElGZe8o-1hmA/Qd<LTF[i*,+d\-#n<?U_4&'
%\pU'ql:?NJlehb1+<o!8!JNU(ej'\j-G#RAaR`]U@\]D!#X+T#BuX0!HL:mG)&qhr'j0e;%lVitOZ:75"2u#7m5'B0SI5h=(J5\R
%ZS$gobNRVEH,jT=9,o@i34LL!Ag?DC<3k0CHK#n_>PKnp]0huXB&$nS5%<#UgnU&LZ(Bqp2./_-U[\h<??TY?B/Ls4eMENDgks?T
%)VN-WOJOF::>*YZcWC[gZ&S9cFc#?RX4s2\NI;%9'qD_;;MlN+bd6'S:?9;p.QZ2fIkIKD&HQ49"[^-qXguWK3Ilai-KD;9lF)h[
%2#mhZN<;i"+fCpRa(qdXjNnTXW\h-g=P0FENG<6Z"Z,=no[X!Mauq0OA,[MuVo#S<4jacJln_6"]c9;a\giiI1Op,eRMY)G[n78A
%-#UV$Dh>/^k3dsj#eoQ?n6`@@Fb%n+,;H=CeF:`o41Mom[_Hak/X(*/CU4g]mFh?`:o';lID0p0mjNb_TKXu/2IR>peHs],aN'$+
%#*t\if8RqVem.i)%;9dg1!C&LhWkEmW5ubL2TV?7R"<q&Cj1,nJa4p89u)Di_^OBe\+eOcmZ%ST!q=J\`)*>HD03Z5SmBV&C?oO`
%Hs>`F"]%9C>6;]Kbg1Q:&DIInh:/$d@p;D*lqXKQhQO/2[BkT2s5Gr/fYrJQdg6J<YOb4rF7P/=a3"%Z:qTWrAZ*03NI;cQmrmLS
%6)\0L=O[V_AM`\FAL;&"C04`q1a<e$26GUMmrH"%[)!tF`k,pc-UVV#d`LQV:!5XPaM[f@LA]ip#b!?-Zfo[ic:GDV2kGZFFq>RK
%CR.qDh@X1;TW7A>Uj\@pc9$3\6pA&Cc=RmmNCHE5OT6n:@)Wj%6[FeY1Y!L;mm'34@5UdBd:1KCdp!dG(+PFOG%B]Dco6Q'j1Jl8
%Bf,V)H,j^(V6c:G7iBYB9P&-QB"*@-9\rXE4G/Dk=+4oB]]4Wb9(P<B2Z3&Wok5\-IJ2sPf&]?&Md_;J>BM]9=4@#j<q:MiSHmHP
%i/YaQK0+o)bs=sA,Lpqnb/YeTjoAZpmEL3p3S67q6]BhV[e5W/n?i4]Ph=su]UCnoSs4F-AS*#CcFY*DI#sLAY!L*joT%<fqZ:lf
%4,uj?V;Fag^N6u`;eV\$>7qp-lS[gU7LeD<$0X:Q[5$d*OS.RqK[R?R7h0ojN/BTZJT1r/.&SF%]L5AOSqDU`%WBHZ["?teN^0sK
%Kl/$Gn"t9(]]8=YO)El0)KU;_8KD\SH@0P%]YfD%/Bpq8LVdD$e[SK5mFAVQ@GWP^=%u+;X,BQON%k4GGaH0.4k[N1'')U[Ka5W0
%8&q%&4;&JS;2ZJ$mpR1r4EdO47l4W_"5Q*a\O62MkjAHTbR?dtFE\=NaIMWmG:,8tf9,JRH^cE.,MO6PQ!(?'ZeZGE"-u]?VnT-H
%NTZ@EG:7QYMimF&nB\,CNUC]*b4X^V5%e8C])t;FV"1SfUs*l*NPCFJ^LJs5AA.U,Q')>8).FRS?/pq=^7\GO9h04BNc9$qO!HXQ
%VDFF+)q?MkXc21'M*l&'!hu5I(QfK02*EYNBhcplpHr]V8`t+dTbrWPGu5[HaNaU$e'SJ*)?A8VWb,PU<./1TBqV,HnX[N&D\iGm
%Q'M%^ZeCLsX"?C(]02&JDQ*#ncJbSaT59uN)isaYBUu8a@>e`ME4%:<4N@uq^Di*WBXP*<o3U-t5$.5UFZraFZ!#g12E=8]Vb$!/
%3i5P^J?X+4YcqH7)EWRfO2;qDn=oGYi;>J7]]57bpplCaK(p(M0HJ6/kWjqZc%/jZ=C,qaR&?Z_4<WX7U?]ZcE)G5[EEW+GNYMkk
%nHrJCDaM.F;e*(1E[]aTkKm[0AMO"s`jE-LecQ6JZ2n;-+p.qtUK:O,$j77Tgr:C[)bgL$II%,Y+GW#AD`Df""*GG!(oS9WEkMki
%eAHKqJU7&6!FTpjfALnZk]j#`G`_5n2`hRI4cfk>C)]Gc/1nTHdqGUYGo9_rN8p5IZ(Mk\*%NO'[u/^l6n'#$A'9BVe@CStkg2"+
%!ZK'u'T+@lLPf!G47*"XQ)Cj@-2'W*W$7nKKR>fQ$W+"lWEfK/a)1nYX9m`lZ'DVAPj=Ld1j07g/PGn9H&fNA!T.4UNqY#@4f12,
%hm\EkZ!2b(jk),7@q2\BH%DEOce&S)jgggFfZREm-UtZeHNf(YcE<M-:`E]ePd*9X$J9mj?>bb2WRU<^;)U7qB3odq'd1t0(>h`"
%pJq;<%O7`UIqTO(^_d1?fVllA:Bnlqd?jsh/9#7VHqT/2MU'e/Zg+%qXK@<gNGYWe48L&%"PFN\r<LroZW;Eh(4]GQHF,RkTpL%f
%^Uo[>2`Kp8:(4(@i.9`uN+e_P,MT2XP?rN[*9hm[\2W7-.0jQt.aK!WX'.]4hg3rndhlQ?V9Z(FCp#>J)]A+Kkq92(Y7ZPpUC1:3
%*!l"(8LX[eA%Vo41V1=W"!JFPKbt]rE[D^hX7N3MIo<,IZ)cqfbNa(C#'L)p@$Ft78=D&fTTVZ((O!e@=+.=e=>:tYOlUqPj"R\2
%9m>m0/]QA0><F4@N>20$>e/M$[4nN8\aEQh!n*Y*0#*kkrRAXZ?4u8OQ^o.WAb8al]lD,QGJ/GZOu<I5AdPR/n7T_DVch<KEp&_8
%9T#D12PN:7)5B2Rd2hVtR$p/Lh-$GjNnbSs;P_'@G%d[]#1QlK(H:1t>+7/r,T2I,\/>BU3g(_s+$C/a;)HJ9pH-oL?Gskrh.Hm$
%lt%;f_]LQCX`:j`"^ij<!00iBG=n2/86>N>$^F[Kl1B_X:7C(E%]1*um#>SA-M)B#bY>.k=RHiC^+&80,A\<ST)ebH.b1m&0`)\&
%kdJ:;!T>W/s.D+Y;^Y[f59GtGl:RW6;i`Z(%%1Plk!re5r_*pURiRrg6F_djd`?AdU9dc$GCFA#c_QtHMO03,Njph!;9Fs,p-<U&
%HsMmW\=lupNi*0O**e^^/ZR>*Z>P3Nn^/;V=0PA;A-n2/pH)PqdXY,;<2HQm%HT[T`T&B7ZS:5'C1d?kkI#RR,f1N`&DVQqJ/\]t
%Ju`*[mAdHXn*!CHJP@h8I`!LAZ^BOtl.#M(:2cKn:8.D^:jP2a?Di%F[puf;Fpf8(*QuH'*]nXMe9,cDHtq`O6iD@VeN;A"GJth6
%.PpYM`Od^DIs-9&WcafA+8P:I%O9SPqdOK;E=VCV(uo.$CYRM/Z_r;Z6NU3M@+6tW\(u#rkEs&k^?%A"0LluBq)p8fC[CobNd+6<
%o\?8..!'6l]IQ\lPh[;\=dg`e[s]$eX"A7e@/a:NIL9T?b](TB(?*[dA#pC"cH;\e)fKhPc7ok\;DQLnT4fa6AjN@5BA*,m<^a:&
%RZuMLn2515kMVmCiNpne""EJi#i/4!jrDK!idLR\JuA+3&S%>?O]Q<FO"7K+jfaA9Vtf'fjdVuqLUiFUF<_!&b-NDgdff\Bf]`-L
%AYX`0r2lL]%rm]iCoePKLS#J(ckA:5?4$$s!l#@50=M,k0^W1BD-92`gU6*i7.._[O!P]2R?"_LN9k,@00arfNPTXL2Q+?*^&+2a
%4&bDk-3LN\;&Ajg4Bqolk\-hNS9hI\XO_aK5V$c149W='$4orOEqqqhC[ph4I2W1T%:bX$!Y]I")Y0ooN)@U:J[(Ji?F>9ZLVoTs
%)d72rm>GQj`+OsUO!*5&;jZ6rj@k6!1hOoiQ,dWs!i()<31?jr0^Km7I`)f$F5`4*1&B]3AES]pVeth\[WZsn.7[q_UFo!s+-rW\
%8C8At=e@l?BS:Ef7:Y!gO_N]W86=+@P0p*b:L8HfjPn=V9gJp>[&+\G([[e$q1c=`iMb@T.`BV4rAjO0kNAkoCMT#-?S.rNL]d[X
%bA[CLdgr19/t[Udg$=uk^YXG>rD-eZ_abGn*\iL+4KMruXnED[:n4tN"l)]';(;b$Q?>W&H"]\MXbs0'?hCt<0NA.mMR"KI9:u+<
%<O"1nj15"qIshm3".)38`o:u\OlEs*MBO#Zce_TONP"8uo5b4Lo%KdAT&;K_iKNtY@+-E:#C(l/d@f7L_AhVoisB?:\bDI1b!f/c
%^+"[hBbt+2XFl.Z!Y/*B,&q2B2MNDn/S*(*j\oTIH^gtHBX>4SVVWU+2::'E'%u(QhH(#sEH+et#W/$d&_;9ZQD0AmY6?Y[X<UEg
%nuI;U>e>OcHcXRrAQ+U!OmV#.MJj#4[lP8h_O]+PLPA:2I"h;#a:j^#.[PF%FR)g(jV5iHRg,G'!n2SH,+$uDS#1"tE8!-.'AB9s
%gGP4'[:WZlf>)(iV&X[gKamZX[nXd5T=dR3h_`i,-s:<?1s]B>l`b)p2.Ps^Bn9\QJ(ukc(:E@)qkiW&>CPA7^h]apC"GVt8mK#G
%fAKtLK:Zr3k_Ckm2/ZX:l7oQPD&_aF=TGdHgU',YXGWCp9/ZXLWosfm.lB$P!(X5FphhNuMA[Yt"s>j.U9e^.p.iR9H`f;.)kG<P
%%(lcKELgOj]775^$8ra7iroXt4Z@!LiNS#m8i*,Y:aF>Rk4eKUC[3Ufq<\"Ihd&GEr:M03mE=DciQH_#rV>W[^V#$cqrck.e@Y,'
%bPkN/^rkh0XcF%;dnDB'[dWnea9eh,/R7Q9XoS]kN,J">R1FW<Y_X,KbLBgSBIZp07La-+dRr*a@@<\V+q7YNSI#[bSpW!fq7ilY
%bE7<$U@'(,Nt-jN-kett.m_`aM%]^4MeX]E]oKQ4c4g-OOiE?(cKBq)(F6n&CJdA%>k*LH/"+iN<]/W'].`b.J_Z>)C[RW_$CVMl
%FFj,%?tF"T*a;>u_KMr<?cPU=g2>e"qVB4%F;JG95I\uP6GfRj-B@TQWn=^T>bi`JL"Un6W,9mVFlU\cFd1`5Yg"/ABdnaUjp[D#
%J!rAq$MrRqk48_*I^Dih>D><#0E@4![KB1q=IlaG71L@'I6B4ijOk)pPK3uJ9<7+R/$Xt:BP-TbgA'_[X^2jH/)\*M8J`4$iY1Uu
%(2oPe&p#*`;/IMK>>$b6nBk'$?*q-cE95M@"Fta,D_afn.OZKLM9j8a:_t_-=WE*`T7[b[20?/I8j]=.NoJp2&A!4O>!*9$E0V<l
%aVW3Yk0TYM/M',hlcY(\%.fA6rh!8>'g/.g^t'G09.p&jk`Vi1i^6.WCE!V`Z"iI8H29VsAjJ"fA0XLi\`iA$>5Cqb9neN?N%(WR
%r]@aY)/FHnk?C"r!Era`'a%V>AlBN*aUC(`pFIp.^QN="M!A6Q6+DV2;d1JY!rB4cV$jh2oP<F/9ri7#JZV(?oR@%IL5eT371R9u
%*F1N[OO8FYTlj0n]\X*XkkDAOB\IBN,UbO_,DT:r\g*f,bl9cjkoG"r0J=%F:kmi9?3i,EO>RTs@n6!07tMsd^hs3I^GCAuh4oSB
%loIt-PDgg9"F^?$f<TpaFlfX`%V;iG]@!$"o=f)I7tcP'O63uUD_sso\\`f3"k$O+`adLdU4n4mAI+lkM(V#c'cH#Ai](hOBV;2R
%NI7olDs1,Y,2Z<=S*G\qooQEd.)JK)]!q3BW"oUM,U&.e'sqj1Q['c>)cA:-O+Kk!iM#E(8*&MKoJlD*;"@WJ]U.K%[cdb'>BAuY
%T\R<&j=:cRkT[D%.Z063['7Nm,.We["n_,`V,^J#J6#FM;sV?D'_HZ;0@7iH\=aY)]X.ksFC1@CAO\"WXh<j=FKrnn%d68Y8c''Q
%K%nj`[&X@5?a%<R<T2?E#Af6qN"O*bI)q,Jhc)k-+@mU+S<1KDqDBl;FV1%2TH,SXCRmgj+EbHWnm6_m[ZF4W!ugR$:D#KgK4Y.d
%(SXYlHrZ3b8dbN+UauEOQ[K5o.[5[<#!/r4nMKND>i_?JfG+_O\ENKM+XDR[1p'li]EeX"/eVE4kXu0%l.G,38@,F\,INtWjH*eF
%.m!ri7DJ,\0@7IP<UL->=[C5ELF`'(`ftHNC5#>7n<;OAc69P"54X;e!T2`Wp7cF'8ulI]n#\"Y7%jma!TlTPB[21=SoqWY4jr;r
%L%\[b0cT9e]a@bHhLPZ^hBbj0JR_r(HRumg9/3it.Dc8DHSkD#gL:pVr'\4h`YCGB'KX&iE.(1GJp%,[Teb>U&Q&)YDa\a0nB2Ar
%SD5ngDNdP0Z"(bj*J7Z1A<-XC\-[cY95#EQlA;ZtJghnL6Jet@j?BT26uWDUTBilO?_Yt6?Q+0<Ys"diof;2(IttAPM"n5r)4A>)
%&%FqBU30V!Mq?PlAZ/80+PoHL#HJNl'>*HYH!5G4b84$rP=YKZ]JZUq?C%Y@B:oN7?j^'R7WPurEN_EdK*if_oZBi]M\"L4J<;.'
%<+Sod5/r=<l%2ki4!W`RGtft?l[Te]F:8Xbd!74iN=4+Tm+ilf(YR(]TZ&tQP1#!nDX:<>1:bq6p_Ve?,k_goCD:rf>P/.bKm3Am
%Oibi@rb,^_s,B-]R\5=g=>F*\_7(Y5*\l'Fci`$g[@:H'P%N\"jMWY\k[&rY+q]f/fGfmr0O3p:,TGmul_QMA6H-mto2&4)JBFQG
%(05+CLgB-WHag-t-r-G-YE7Fj6;KMdW=0N:6W3//"uNgl_i!>?[]pXpUUlN"^Cjc9^opnn,L(TeJkcaFF+0?s^oGaN*8hu@MX?cj
%,X>1[(h"BNjKlN<Xs.G+(IP/u@C[.Y`_',(;D)M"/*2?kG;`gIi_^GI;(353KPL\2j?'(f!^r/V2PM\p,foj,CL+5];W1KfK5-+Q
%M9`-D+'59mYKBm:BY[bQ7N@2EiH?0jOI;Z7-r.8t__#^D,.m.EOHr:i!/;a5*GTFkoq?G(98m8_elAi=6e\a5p1^^"O1.4/M-2K,
%"Hu.e^Xd(Eh7Y!bbD#RJ_@bR-LdHc[iD3e'Lak0V@)1pqSdfWY(A-[a'(JWsi0`I)'&"7H!2/VD`l2R.PjdMK.+D5Dgcs"EH[/J.
%-r1A(TQ/ChCP/@\,18%CeCLH*`Q/88\?(GK6WqU7F/Dms/\hGPdaFWOe/"0ZF:1I8KpPU[gY<qDHuGdqid;"K-`'f`3;@W0QW"S,
%a/WIfM`0B>/@ZAKe[g>4:2$$3,H?EslU<npdE`.?p#'rXQ+QP"eti6A-*$]nVc@l\S>5h>?9@!=Jqp8U@=d[dVc(4<L.U'AFN/PY
%)p4.E&])nY''s3N>f1/K)lIs\VrfnU;[i%-Er3#c*Pc.3(Z05'9@R*Eqa<N-OO<b-BI\3YAqc$u343A#MHt^+QcoIcL"`!ja-Cnl
%6+um$.8W*q#u%ijT"-,^,2b$_Z$WtNpo"5pk;;O[V!cgQZ$P:[+ecB)Y81ssKTq4lD'g/_8,;U;@kfuH*tbKGTbhcdf`pXrTMAM+
%aO@ZO9N_L&8^hgl*J8kQeWu^ZNYL+S>eA2s255O?h@MP"]d!/Bd<T-SN2/g'(9gZY>t3pGp5Pd,U%7m9%?ObE+&UD/^]CHF+a&f9
%/[]/6%Gd\W[e/Qn0J$UGf:k8=oM$imC3S4P%t^2t'r6:bn30]>'AI*Y?>ZR%4D!AV.^O]?^T&K:W5L@7!tCqhr$\BV_K[mpWM,YY
%\/%!<pFr.]\VOpRKHJNo#7n5r-knp@)d=XeoEBp?7]pbqSB@jF8?;t7De]e#>1d#C9:VI7Y;"@/=c.-]I%!bTnFT%+)2c!!W*N$m
%P_05S-"*hl)SmWH^&g8^AWh<bE#Y_JAb$"s55Y,M:L=&RIJMXd^4,qDkI3[gH6iZcI/_YTDu]UM=5U`=qnpGrIIlK#n)%HcqX-F5
%Fl32-r9Npu%rZsql2)RZ?@Mgqp]#jXHtiJp-_L>15QCE:O8]sT.D%P~>
%AI9_PrivateDataEnd
